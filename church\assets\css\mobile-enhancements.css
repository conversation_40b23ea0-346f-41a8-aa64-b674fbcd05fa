/**
 * Mobile Responsiveness Enhancements
 * Comprehensive mobile-first improvements for church campaign application
 * Version: 1.0.0
 */

/* Mobile-First Base Styles */
:root {
    --mobile-padding: 1rem;
    --mobile-margin: 0.75rem;
    --touch-target-size: 44px;
    --mobile-font-size: 16px;
    --mobile-line-height: 1.5;
    --mobile-border-radius: 8px;
    --mobile-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --mobile-transition: all 0.3s ease;
}

/* Prevent zoom on input focus (iOS Safari) */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="tel"],
input[type="url"],
input[type="search"],
textarea,
select {
    font-size: 16px !important;
    -webkit-appearance: none;
    border-radius: var(--mobile-border-radius);
}

/* Touch-friendly buttons */
.btn,
button,
[role="button"],
.btn-link {
    min-height: var(--touch-target-size);
    min-width: var(--touch-target-size);
    padding: 0.75rem 1.5rem;
    border-radius: var(--mobile-border-radius);
    transition: var(--mobile-transition);
    -webkit-tap-highlight-color: transparent;
}

/* Mobile-optimized cards */
.card {
    border-radius: var(--mobile-border-radius);
    box-shadow: var(--mobile-shadow);
    margin-bottom: var(--mobile-margin);
    overflow: hidden;
}

.card-body {
    padding: var(--mobile-padding);
}

/* Mobile navigation improvements */
.navbar {
    padding: 0.5rem var(--mobile-padding);
}

.navbar-toggler {
    border: none;
    padding: 0.5rem;
    min-height: var(--touch-target-size);
    min-width: var(--touch-target-size);
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 2px rgba(var(--bs-primary-rgb), 0.25);
}

/* Mobile dropdown improvements */
.dropdown-menu {
    border-radius: var(--mobile-border-radius);
    box-shadow: var(--mobile-shadow);
    border: none;
    margin-top: 0.5rem;
}

.dropdown-item {
    padding: 0.75rem 1rem;
    min-height: var(--touch-target-size);
    display: flex;
    align-items: center;
}

/* Mobile table responsiveness */
.table-responsive {
    border-radius: var(--mobile-border-radius);
    overflow: hidden;
}

.table {
    margin-bottom: 0;
}

.table td,
.table th {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
}

/* Mobile form improvements */
.form-control,
.form-select {
    padding: 0.75rem;
    border-radius: var(--mobile-border-radius);
    border: 1px solid #dee2e6;
    transition: var(--mobile-transition);
}

.form-control:focus,
.form-select:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 2px rgba(var(--bs-primary-rgb), 0.25);
}

.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* Mobile modal improvements */
.modal-dialog {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
}

.modal-content {
    border-radius: var(--mobile-border-radius);
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: var(--mobile-padding);
    border-bottom: 1px solid #dee2e6;
}

.modal-body {
    padding: var(--mobile-padding);
}

.modal-footer {
    padding: var(--mobile-padding);
    border-top: 1px solid #dee2e6;
}

/* Mobile alert improvements */
.alert {
    border-radius: var(--mobile-border-radius);
    padding: var(--mobile-padding);
    margin-bottom: var(--mobile-margin);
}

/* Mobile badge improvements */
.badge {
    padding: 0.5rem 0.75rem;
    border-radius: calc(var(--mobile-border-radius) / 2);
}

/* Mobile pagination */
.pagination {
    justify-content: center;
    flex-wrap: wrap;
}

.page-link {
    min-height: var(--touch-target-size);
    min-width: var(--touch-target-size);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--mobile-border-radius);
    margin: 0 2px;
}

/* Mobile-specific utilities */
.mobile-only {
    display: none;
}

.desktop-only {
    display: block;
}

.mobile-center {
    text-align: center;
}

.mobile-full-width {
    width: 100%;
}

.mobile-hidden {
    display: none;
}

/* Responsive breakpoints */
@media (max-width: 575.98px) {
    /* Extra small devices */
    .mobile-only {
        display: block;
    }
    
    .desktop-only {
        display: none;
    }
    
    .container,
    .container-fluid {
        padding-left: var(--mobile-padding);
        padding-right: var(--mobile-padding);
    }
    
    .row {
        margin-left: calc(-1 * var(--mobile-margin) / 2);
        margin-right: calc(-1 * var(--mobile-margin) / 2);
    }
    
    .col,
    [class*="col-"] {
        padding-left: calc(var(--mobile-margin) / 2);
        padding-right: calc(var(--mobile-margin) / 2);
        margin-bottom: var(--mobile-margin);
    }
    
    /* Stack columns on mobile */
    .col-md-6,
    .col-lg-4,
    .col-lg-6,
    .col-xl-3,
    .col-xl-4 {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    /* Mobile typography */
    h1 { font-size: 1.75rem; }
    h2 { font-size: 1.5rem; }
    h3 { font-size: 1.25rem; }
    h4 { font-size: 1.1rem; }
    h5 { font-size: 1rem; }
    h6 { font-size: 0.9rem; }
    
    /* Mobile spacing */
    .mb-3 { margin-bottom: var(--mobile-margin) !important; }
    .mt-3 { margin-top: var(--mobile-margin) !important; }
    .p-3 { padding: var(--mobile-padding) !important; }
    
    /* Mobile buttons */
    .btn-group {
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group .btn {
        margin-bottom: 0.5rem;
        border-radius: var(--mobile-border-radius) !important;
    }
    
    /* Mobile tables */
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .table th,
    .table td {
        padding: 0.5rem 0.25rem;
    }
    
    /* Hide less important columns on mobile */
    .mobile-hide {
        display: none;
    }
}

@media (max-width: 767.98px) {
    /* Small devices */
    .navbar-nav {
        padding-top: 1rem;
    }
    
    .navbar-nav .nav-link {
        padding: 0.75rem 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .navbar-nav .nav-link:last-child {
        border-bottom: none;
    }
    
    /* Mobile card adjustments */
    .card-columns {
        column-count: 1;
    }
    
    .card-deck .card {
        margin-bottom: var(--mobile-margin);
    }
}

@media (max-width: 991.98px) {
    /* Medium devices */
    .col-lg-4 {
        flex: 0 0 50%;
        max-width: 50%;
    }
}

/* Landscape orientation adjustments */
@media (max-height: 500px) and (orientation: landscape) {
    .modal-dialog {
        margin: 0.5rem;
        max-height: calc(100vh - 1rem);
    }
    
    .modal-content {
        max-height: 100%;
        overflow-y: auto;
    }
    
    .navbar {
        padding: 0.25rem var(--mobile-padding);
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .btn,
    .card,
    .form-control {
        border-width: 0.5px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .form-control,
    .form-select {
        background-color: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .card {
        background-color: #2d3748;
        border-color: #4a5568;
    }
    
    .modal-content {
        background-color: #2d3748;
        color: #e2e8f0;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Print styles */
@media print {
    .mobile-only,
    .navbar,
    .btn,
    .modal {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000;
        box-shadow: none;
    }
}
