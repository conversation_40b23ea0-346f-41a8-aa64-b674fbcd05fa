<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

require_once '../config.php';
require_once '../includes/sms_functions.php';

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['create_campaign'])) {
            $campaign_name = trim($_POST['campaign_name']);
            $template_id = $_POST['template_id'] ?? null;
            $message_content = trim($_POST['message_content']);
            $target_audience = $_POST['target_audience'];
            $scheduled_at = $_POST['scheduled_at'] ?? null;
            
            if (empty($campaign_name) || empty($message_content)) {
                throw new Exception("Campaign name and message content are required");
            }
            
            // Create campaign
            $stmt = $pdo->prepare("
                INSERT INTO sms_campaigns (campaign_name, template_id, message_content, target_audience, scheduled_at, created_by)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([$campaign_name, $template_id, $message_content, $target_audience, $scheduled_at, $_SESSION['admin_id']]);
            
            $campaign_id = $pdo->lastInsertId();
            
            // Get recipients based on target audience
            $recipients = [];
            switch ($target_audience) {
                case 'all_members':
                    $stmt = $pdo->prepare("SELECT id as member_id, full_name, phone_number FROM members WHERE phone_number IS NOT NULL AND phone_number != '' AND sms_notifications = 1");
                    $stmt->execute();
                    $recipients = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    break;
                    
                case 'birthday_today':
                    $stmt = $pdo->prepare("SELECT id as member_id, full_name, phone_number FROM members WHERE DATE_FORMAT(birth_date, '%m-%d') = DATE_FORMAT(NOW(), '%m-%d') AND phone_number IS NOT NULL AND phone_number != '' AND sms_notifications = 1");
                    $stmt->execute();
                    $recipients = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    break;
            }
            
            // Add recipients to queue
            foreach ($recipients as $recipient) {
                $personalized_message = str_replace('{name}', $recipient['full_name'], $message_content);
                
                $stmt = $pdo->prepare("
                    INSERT INTO sms_queue (campaign_id, member_id, phone_number, message, sms_type, scheduled_at)
                    VALUES (?, ?, ?, ?, 'bulk', ?)
                ");
                $stmt->execute([
                    $campaign_id,
                    $recipient['member_id'],
                    $recipient['phone_number'],
                    $personalized_message,
                    $scheduled_at ?: date('Y-m-d H:i:s')
                ]);
            }
            
            // Update campaign with recipient count
            $stmt = $pdo->prepare("UPDATE sms_campaigns SET total_recipients = ? WHERE id = ?");
            $stmt->execute([count($recipients), $campaign_id]);
            
            $message = "SMS campaign '{$campaign_name}' created successfully with " . count($recipients) . " recipients.";
            
        } elseif (isset($_POST['send_campaign'])) {
            $campaign_id = $_POST['campaign_id'];
            
            // Update campaign status
            $stmt = $pdo->prepare("UPDATE sms_campaigns SET status = 'sending', started_at = NOW() WHERE id = ?");
            $stmt->execute([$campaign_id]);
            
            // Process queue for this campaign
            $stmt = $pdo->prepare("SELECT * FROM sms_queue WHERE campaign_id = ? AND status = 'pending' ORDER BY priority DESC, created_at ASC LIMIT 50");
            $stmt->execute([$campaign_id]);
            $queue_items = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $sms_manager = getSMSManager();
            $sent_count = 0;
            $failed_count = 0;
            
            foreach ($queue_items as $item) {
                // Update status to processing
                $stmt = $pdo->prepare("UPDATE sms_queue SET status = 'processing' WHERE id = ?");
                $stmt->execute([$item['id']]);
                
                // Send SMS
                $result = $sms_manager->sendSMS($item['phone_number'], $item['message'], $item['sms_type'], $item['member_id']);
                
                if ($result) {
                    $stmt = $pdo->prepare("UPDATE sms_queue SET status = 'sent', processed_at = NOW() WHERE id = ?");
                    $stmt->execute([$item['id']]);
                    $sent_count++;
                } else {
                    $stmt = $pdo->prepare("UPDATE sms_queue SET status = 'failed', processed_at = NOW(), attempts = attempts + 1 WHERE id = ?");
                    $stmt->execute([$item['id']]);
                    $failed_count++;
                }
            }
            
            // Update campaign counts
            $stmt = $pdo->prepare("UPDATE sms_campaigns SET sent_count = sent_count + ?, failed_count = failed_count + ? WHERE id = ?");
            $stmt->execute([$sent_count, $failed_count, $campaign_id]);
            
            // Check if campaign is complete
            $stmt = $pdo->prepare("SELECT total_recipients, sent_count, failed_count FROM sms_campaigns WHERE id = ?");
            $stmt->execute([$campaign_id]);
            $campaign = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (($campaign['sent_count'] + $campaign['failed_count']) >= $campaign['total_recipients']) {
                $stmt = $pdo->prepare("UPDATE sms_campaigns SET status = 'completed', completed_at = NOW() WHERE id = ?");
                $stmt->execute([$campaign_id]);
            }
            
            $message = "Campaign batch processed: {$sent_count} sent, {$failed_count} failed.";
        }
        
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Get SMS templates
$stmt = $pdo->prepare("SELECT id, template_name, template_content FROM sms_templates WHERE is_active = 1 ORDER BY template_name");
$stmt->execute();
$sms_templates = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get campaigns
$stmt = $pdo->prepare("
    SELECT c.*, 
           CASE 
               WHEN c.total_recipients > 0 THEN ROUND((c.sent_count / c.total_recipients) * 100, 1)
               ELSE 0 
           END as progress_percentage
    FROM sms_campaigns c 
    ORDER BY c.created_at DESC 
    LIMIT 20
");
$stmt->execute();
$campaigns = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get SMS settings to check if SMS is enabled
$stmt = $pdo->prepare("SELECT setting_key, setting_value FROM sms_settings WHERE setting_key IN ('sms_notifications_enabled', 'twilio_enabled', 'nexmo_enabled')");
$stmt->execute();
$settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

$sms_enabled = ($settings['sms_notifications_enabled'] ?? '0') === '1';
$provider_enabled = ($settings['twilio_enabled'] ?? '0') === '1' || ($settings['nexmo_enabled'] ?? '0') === '1';

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-paper-plane me-2"></i>SMS Campaigns</h2>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createCampaignModal">
                    <i class="fas fa-plus me-2"></i>Create New Campaign
                </button>
            </div>
            
            <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if (!$sms_enabled || !$provider_enabled): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    SMS functionality is not fully configured. 
                    <a href="sms_integration.php" class="alert-link">Configure SMS settings</a> to enable campaigns.
                </div>
            <?php endif; ?>
            
            <!-- Campaigns List -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>Recent Campaigns</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($campaigns)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-paper-plane fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No SMS campaigns yet</h5>
                            <p class="text-muted">Create your first SMS campaign to get started.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Campaign Name</th>
                                        <th>Target Audience</th>
                                        <th>Recipients</th>
                                        <th>Progress</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($campaigns as $campaign): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($campaign['campaign_name']); ?></strong>
                                                <br>
                                                <small class="text-muted"><?php echo substr(htmlspecialchars($campaign['message_content']), 0, 50) . '...'; ?></small>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?php echo ucfirst(str_replace('_', ' ', $campaign['target_audience'])); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="text-center">
                                                    <strong><?php echo $campaign['total_recipients']; ?></strong>
                                                    <br>
                                                    <small class="text-success"><?php echo $campaign['sent_count']; ?> sent</small>
                                                    <?php if ($campaign['failed_count'] > 0): ?>
                                                        <br><small class="text-danger"><?php echo $campaign['failed_count']; ?> failed</small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar" role="progressbar" 
                                                         style="width: <?php echo $campaign['progress_percentage']; ?>%"
                                                         aria-valuenow="<?php echo $campaign['progress_percentage']; ?>" 
                                                         aria-valuemin="0" aria-valuemax="100">
                                                        <?php echo $campaign['progress_percentage']; ?>%
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <?php
                                                $status_class = [
                                                    'draft' => 'secondary',
                                                    'scheduled' => 'warning',
                                                    'sending' => 'primary',
                                                    'completed' => 'success',
                                                    'cancelled' => 'danger'
                                                ];
                                                ?>
                                                <span class="badge bg-<?php echo $status_class[$campaign['status']] ?? 'secondary'; ?>">
                                                    <?php echo ucfirst($campaign['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php echo date('M j, Y g:i A', strtotime($campaign['created_at'])); ?>
                                            </td>
                                            <td>
                                                <?php if ($campaign['status'] === 'draft' && $sms_enabled && $provider_enabled): ?>
                                                    <form method="post" style="display: inline;">
                                                        <input type="hidden" name="campaign_id" value="<?php echo $campaign['id']; ?>">
                                                        <button type="submit" name="send_campaign" class="btn btn-sm btn-success" 
                                                                onclick="return confirm('Start sending this campaign?')">
                                                            <i class="fas fa-play me-1"></i>Send
                                                        </button>
                                                    </form>
                                                <?php endif; ?>
                                                
                                                <?php if ($campaign['status'] === 'sending' && $sms_enabled && $provider_enabled): ?>
                                                    <form method="post" style="display: inline;">
                                                        <input type="hidden" name="campaign_id" value="<?php echo $campaign['id']; ?>">
                                                        <button type="submit" name="send_campaign" class="btn btn-sm btn-primary">
                                                            <i class="fas fa-forward me-1"></i>Continue
                                                        </button>
                                                    </form>
                                                <?php endif; ?>
                                                
                                                <button class="btn btn-sm btn-outline-info" 
                                                        onclick="viewCampaignDetails(<?php echo $campaign['id']; ?>)">
                                                    <i class="fas fa-eye me-1"></i>View
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Campaign Modal -->
<div class="modal fade" id="createCampaignModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form method="post">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Create SMS Campaign</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="campaign_name" class="form-label">Campaign Name *</label>
                                <input type="text" class="form-control" id="campaign_name" name="campaign_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="target_audience" class="form-label">Target Audience *</label>
                                <select class="form-select" id="target_audience" name="target_audience" required>
                                    <option value="">Select audience...</option>
                                    <option value="all_members">All Members</option>
                                    <option value="birthday_today">Birthday Today</option>
                                    <option value="event_attendees">Event Attendees</option>
                                    <option value="custom_group">Custom Group</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="template_id" class="form-label">Use Template (Optional)</label>
                        <select class="form-select" id="template_id" name="template_id" onchange="loadTemplate()">
                            <option value="">Select a template...</option>
                            <?php foreach ($sms_templates as $template): ?>
                                <option value="<?php echo $template['id']; ?>"
                                        data-content="<?php echo htmlspecialchars($template['template_content']); ?>">
                                    <?php echo htmlspecialchars($template['template_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="message_content" class="form-label">Message Content *</label>
                        <textarea class="form-control" id="message_content" name="message_content" rows="4"
                                  maxlength="160" required placeholder="Enter your SMS message..."></textarea>
                        <div class="form-text">
                            <span id="char_count">0</span>/160 characters.
                            Available placeholders: {name}, {church_name}
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="scheduled_at" class="form-label">Schedule Send Time (Optional)</label>
                        <input type="datetime-local" class="form-control" id="scheduled_at" name="scheduled_at">
                        <div class="form-text">Leave empty to send immediately</div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Preview:</strong> Your message will be personalized for each recipient.
                        <div id="message_preview" class="mt-2 p-2 bg-light rounded">
                            <em>Type your message to see preview...</em>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="create_campaign" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Create Campaign
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Character counter for SMS message
document.getElementById('message_content').addEventListener('input', function() {
    const content = this.value;
    const charCount = content.length;
    document.getElementById('char_count').textContent = charCount;

    // Update preview
    const preview = content.replace('{name}', 'John Doe').replace('{church_name}', 'Freedom Assembly Church');
    document.getElementById('message_preview').innerHTML = preview || '<em>Type your message to see preview...</em>';

    // Color coding for character count
    const charCountElement = document.getElementById('char_count');
    if (charCount > 160) {
        charCountElement.className = 'text-danger fw-bold';
    } else if (charCount > 140) {
        charCountElement.className = 'text-warning fw-bold';
    } else {
        charCountElement.className = 'text-success';
    }
});

// Load template content
function loadTemplate() {
    const select = document.getElementById('template_id');
    const selectedOption = select.options[select.selectedIndex];
    const content = selectedOption.getAttribute('data-content');

    if (content) {
        document.getElementById('message_content').value = content;
        document.getElementById('message_content').dispatchEvent(new Event('input'));
    }
}

// View campaign details
function viewCampaignDetails(campaignId) {
    // This would open a modal or navigate to a details page
    alert('Campaign details view - Campaign ID: ' + campaignId);
}

// Set minimum datetime for scheduling
document.addEventListener('DOMContentLoaded', function() {
    const now = new Date();
    now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
    document.getElementById('scheduled_at').min = now.toISOString().slice(0, 16);
});
</script>

<?php include 'includes/footer.php'; ?>
