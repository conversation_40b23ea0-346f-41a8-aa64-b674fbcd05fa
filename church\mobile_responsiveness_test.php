<?php
/**
 * Mobile Responsiveness Test Script
 * Comprehensive testing of mobile enhancements and PWA features
 */

require_once 'config.php';

// Test results storage
$testResults = [];
$totalTests = 0;
$passedTests = 0;

function runTest($testName, $testFunction) {
    global $testResults, $totalTests, $passedTests;
    
    $totalTests++;
    echo "<div class='test-item'>";
    echo "<h6><i class='bi bi-gear-fill'></i> $testName</h6>";
    
    try {
        $result = $testFunction();
        if ($result['success']) {
            $passedTests++;
            echo "<div class='alert alert-success'><i class='bi bi-check-circle'></i> {$result['message']}</div>";
        } else {
            echo "<div class='alert alert-warning'><i class='bi bi-exclamation-triangle'></i> {$result['message']}</div>";
        }
        $testResults[$testName] = $result;
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'><i class='bi bi-x-circle'></i> Error: {$e->getMessage()}</div>";
        $testResults[$testName] = ['success' => false, 'message' => $e->getMessage()];
    }
    
    echo "</div>";
}

// Test 1: Mobile CSS Files
function testMobileCSSFiles() {
    $mobileCSS = 'assets/css/mobile-enhancements.css';
    if (file_exists($mobileCSS)) {
        $content = file_get_contents($mobileCSS);
        $hasResponsiveBreakpoints = strpos($content, '@media (max-width:') !== false;
        $hasTouchTargets = strpos($content, '--touch-target-size') !== false;
        $hasMobileUtilities = strpos($content, '.mobile-only') !== false;
        
        if ($hasResponsiveBreakpoints && $hasTouchTargets && $hasMobileUtilities) {
            return ['success' => true, 'message' => 'Mobile CSS file exists with responsive breakpoints, touch targets, and mobile utilities'];
        } else {
            return ['success' => false, 'message' => 'Mobile CSS file missing key responsive features'];
        }
    }
    return ['success' => false, 'message' => 'Mobile CSS file not found'];
}

// Test 2: Mobile JavaScript Enhancements
function testMobileJavaScript() {
    $mobileJS = 'assets/js/mobile-enhancements.js';
    if (file_exists($mobileJS)) {
        $content = file_get_contents($mobileJS);
        $hasTouchDetection = strpos($content, 'ontouchstart') !== false;
        $hasSwipeGestures = strpos($content, 'swipeLeft') !== false;
        $hasMobileOptimizations = strpos($content, 'MobileEnhancements') !== false;
        
        if ($hasTouchDetection && $hasSwipeGestures && $hasMobileOptimizations) {
            return ['success' => true, 'message' => 'Mobile JavaScript includes touch detection, swipe gestures, and mobile optimizations'];
        } else {
            return ['success' => false, 'message' => 'Mobile JavaScript missing key mobile features'];
        }
    }
    return ['success' => false, 'message' => 'Mobile JavaScript file not found'];
}

// Test 3: PWA Manifest
function testPWAManifest() {
    $manifest = 'manifest.json';
    if (file_exists($manifest)) {
        $content = file_get_contents($manifest);
        $manifestData = json_decode($content, true);
        
        if ($manifestData && isset($manifestData['name']) && isset($manifestData['icons']) && isset($manifestData['start_url'])) {
            return ['success' => true, 'message' => 'PWA manifest exists with required fields: name, icons, start_url'];
        } else {
            return ['success' => false, 'message' => 'PWA manifest missing required fields'];
        }
    }
    return ['success' => false, 'message' => 'PWA manifest file not found'];
}

// Test 4: Service Worker
function testServiceWorker() {
    $serviceWorker = 'sw.js';
    if (file_exists($serviceWorker)) {
        $content = file_get_contents($serviceWorker);
        $hasInstallEvent = strpos($content, "addEventListener('install'") !== false;
        $hasFetchEvent = strpos($content, "addEventListener('fetch'") !== false;
        $hasCaching = strpos($content, 'caches.open') !== false;
        
        if ($hasInstallEvent && $hasFetchEvent && $hasCaching) {
            return ['success' => true, 'message' => 'Service worker includes install, fetch events and caching functionality'];
        } else {
            return ['success' => false, 'message' => 'Service worker missing key PWA features'];
        }
    }
    return ['success' => false, 'message' => 'Service worker file not found'];
}

// Test 5: Offline Page
function testOfflinePage() {
    $offlinePage = 'user/offline.html';
    if (file_exists($offlinePage)) {
        $content = file_get_contents($offlinePage);
        $hasRetryFunction = strpos($content, 'retryConnection') !== false;
        $hasConnectionStatus = strpos($content, 'connectionStatus') !== false;
        $hasMobileStyles = strpos($content, '@media (max-width: 768px)') !== false;
        
        if ($hasRetryFunction && $hasConnectionStatus && $hasMobileStyles) {
            return ['success' => true, 'message' => 'Offline page includes retry functionality, connection status, and mobile styles'];
        } else {
            return ['success' => false, 'message' => 'Offline page missing key features'];
        }
    }
    return ['success' => false, 'message' => 'Offline page not found'];
}

// Test 6: Dashboard Mobile Integration
function testDashboardMobileIntegration() {
    $dashboard = 'user/dashboard.php';
    if (file_exists($dashboard)) {
        $content = file_get_contents($dashboard);
        $hasMobileCSS = strpos($content, 'mobile-enhancements.css') !== false;
        $hasMobileJS = strpos($content, 'mobile-enhancements.js') !== false;
        $hasPWAManifest = strpos($content, 'manifest.json') !== false;
        $hasServiceWorker = strpos($content, 'serviceWorker.register') !== false;
        
        if ($hasMobileCSS && $hasMobileJS && $hasPWAManifest && $hasServiceWorker) {
            return ['success' => true, 'message' => 'Dashboard includes mobile CSS, JS, PWA manifest, and service worker registration'];
        } else {
            return ['success' => false, 'message' => 'Dashboard missing mobile integration components'];
        }
    }
    return ['success' => false, 'message' => 'Dashboard file not found'];
}

// Test 7: Events Page Mobile Integration
function testEventsPageMobileIntegration() {
    $events = 'user/events.php';
    if (file_exists($events)) {
        $content = file_get_contents($events);
        $hasMobileCSS = strpos($content, 'mobile-enhancements.css') !== false;
        $hasMobileJS = strpos($content, 'mobile-enhancements.js') !== false;
        
        if ($hasMobileCSS && $hasMobileJS) {
            return ['success' => true, 'message' => 'Events page includes mobile CSS and JavaScript enhancements'];
        } else {
            return ['success' => false, 'message' => 'Events page missing mobile integration'];
        }
    }
    return ['success' => false, 'message' => 'Events page not found'];
}

// Test 8: Birthday Templates Mobile Integration
function testBirthdayTemplatesMobileIntegration() {
    $birthdays = 'user/birthday_templates.php';
    if (file_exists($birthdays)) {
        $content = file_get_contents($birthdays);
        $hasMobileCSS = strpos($content, 'mobile-enhancements.css') !== false;
        $hasMobileJS = strpos($content, 'mobile-enhancements.js') !== false;
        $hasResponsiveGrid = strpos($content, 'grid-template-columns') !== false;
        
        if ($hasMobileCSS && $hasMobileJS && $hasResponsiveGrid) {
            return ['success' => true, 'message' => 'Birthday templates page includes mobile enhancements and responsive grid'];
        } else {
            return ['success' => false, 'message' => 'Birthday templates page missing mobile features'];
        }
    }
    return ['success' => false, 'message' => 'Birthday templates page not found'];
}

// Test 9: Viewport Meta Tags
function testViewportMetaTags() {
    $pages = ['user/dashboard.php', 'user/events.php', 'user/birthday_templates.php'];
    $pagesWithViewport = 0;
    
    foreach ($pages as $page) {
        if (file_exists($page)) {
            $content = file_get_contents($page);
            if (strpos($content, 'name="viewport"') !== false) {
                $pagesWithViewport++;
            }
        }
    }
    
    if ($pagesWithViewport === count($pages)) {
        return ['success' => true, 'message' => 'All user pages include proper viewport meta tags'];
    } else {
        return ['success' => false, 'message' => "Only $pagesWithViewport out of " . count($pages) . " pages have viewport meta tags"];
    }
}

// Test 10: Bootstrap Integration
function testBootstrapIntegration() {
    $dashboard = 'user/dashboard.php';
    if (file_exists($dashboard)) {
        $content = file_get_contents($dashboard);
        $hasBootstrapCSS = strpos($content, 'bootstrap@5.3.0') !== false;
        $hasBootstrapJS = strpos($content, 'bootstrap.bundle.min.js') !== false;
        $hasBootstrapIcons = strpos($content, 'bootstrap-icons') !== false;
        
        if ($hasBootstrapCSS && $hasBootstrapJS && $hasBootstrapIcons) {
            return ['success' => true, 'message' => 'Bootstrap 5.3.0 CSS, JS, and Icons properly integrated'];
        } else {
            return ['success' => false, 'message' => 'Bootstrap integration incomplete'];
        }
    }
    return ['success' => false, 'message' => 'Cannot verify Bootstrap integration'];
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Responsiveness Test Results</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="assets/css/mobile-enhancements.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        
        .test-container {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .test-item {
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 1rem;
            margin-bottom: 1rem;
        }
        
        .test-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .score-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
            margin: 0 auto 1rem;
        }
        
        .score-excellent { background: linear-gradient(135deg, #28a745, #20c997); }
        .score-good { background: linear-gradient(135deg, #ffc107, #fd7e14); }
        .score-poor { background: linear-gradient(135deg, #dc3545, #e83e8c); }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <div class="text-center mb-4">
                <h1><i class="bi bi-phone"></i> Mobile Responsiveness Test Results</h1>
                <p class="text-muted">Comprehensive testing of mobile enhancements and PWA features</p>
            </div>
            
            <?php
            // Run all tests
            runTest('Mobile CSS Files', 'testMobileCSSFiles');
            runTest('Mobile JavaScript Enhancements', 'testMobileJavaScript');
            runTest('PWA Manifest', 'testPWAManifest');
            runTest('Service Worker', 'testServiceWorker');
            runTest('Offline Page', 'testOfflinePage');
            runTest('Dashboard Mobile Integration', 'testDashboardMobileIntegration');
            runTest('Events Page Mobile Integration', 'testEventsPageMobileIntegration');
            runTest('Birthday Templates Mobile Integration', 'testBirthdayTemplatesMobileIntegration');
            runTest('Viewport Meta Tags', 'testViewportMetaTags');
            runTest('Bootstrap Integration', 'testBootstrapIntegration');
            
            // Calculate score
            $score = ($passedTests / $totalTests) * 100;
            $scoreClass = $score >= 90 ? 'score-excellent' : ($score >= 70 ? 'score-good' : 'score-poor');
            ?>
            
            <div class="text-center mt-4">
                <div class="<?php echo $scoreClass; ?> score-circle">
                    <?php echo round($score, 1); ?>%
                </div>
                <h3>Mobile Responsiveness Score</h3>
                <p class="text-muted"><?php echo $passedTests; ?> out of <?php echo $totalTests; ?> tests passed</p>
                
                <?php if ($score >= 90): ?>
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle"></i> <strong>Excellent!</strong> Your mobile responsiveness implementation is outstanding.
                    </div>
                <?php elseif ($score >= 70): ?>
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i> <strong>Good!</strong> Your mobile implementation is solid with room for improvement.
                    </div>
                <?php else: ?>
                    <div class="alert alert-danger">
                        <i class="bi bi-x-circle"></i> <strong>Needs Work!</strong> Several mobile features need attention.
                    </div>
                <?php endif; ?>
                
                <div class="mt-3">
                    <a href="user/dashboard.php" class="btn btn-primary me-2">
                        <i class="bi bi-speedometer2"></i> Test Dashboard
                    </a>
                    <a href="user/events.php" class="btn btn-outline-primary me-2">
                        <i class="bi bi-calendar-event"></i> Test Events
                    </a>
                    <a href="user/birthday_templates.php" class="btn btn-outline-primary">
                        <i class="bi bi-gift"></i> Test Birthdays
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/mobile-enhancements.js"></script>
</body>
</html>
