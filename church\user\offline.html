<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Church Campaign Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="../assets/css/mobile-enhancements.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .offline-container {
            background: white;
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
            margin: 2rem;
        }
        
        .offline-icon {
            font-size: 4rem;
            color: #6c757d;
            margin-bottom: 1.5rem;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .offline-title {
            color: #495057;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        
        .offline-message {
            color: #6c757d;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .retry-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 50px;
            color: white;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .retry-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
            color: white;
        }
        
        .offline-features {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 2rem;
            text-align: left;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
        }
        
        .feature-item:last-child {
            margin-bottom: 0;
        }
        
        .feature-icon {
            color: #28a745;
            margin-right: 0.75rem;
            font-size: 1.1rem;
        }
        
        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
            z-index: 1000;
        }
        
        .status-offline {
            background: #dc3545;
            color: white;
        }
        
        .status-online {
            background: #28a745;
            color: white;
        }
        
        @media (max-width: 768px) {
            .offline-container {
                margin: 1rem;
                padding: 2rem;
            }
            
            .offline-icon {
                font-size: 3rem;
            }
            
            .connection-status {
                top: 10px;
                right: 10px;
                font-size: 0.75rem;
                padding: 0.375rem 0.75rem;
            }
        }
    </style>
</head>
<body>
    <div class="connection-status status-offline" id="connectionStatus">
        <i class="bi bi-wifi-off"></i> Offline
    </div>
    
    <div class="offline-container">
        <div class="offline-icon">
            <i class="bi bi-wifi-off"></i>
        </div>
        
        <h2 class="offline-title">You're Currently Offline</h2>
        
        <p class="offline-message">
            It looks like you've lost your internet connection. Don't worry - some features are still available while you're offline!
        </p>
        
        <a href="javascript:void(0)" onclick="retryConnection()" class="retry-btn">
            <i class="bi bi-arrow-clockwise"></i> Try Again
        </a>
        
        <div class="offline-features">
            <h6 class="mb-3"><i class="bi bi-check-circle text-success"></i> Available Offline:</h6>
            
            <div class="feature-item">
                <i class="bi bi-eye feature-icon"></i>
                <span>View cached dashboard content</span>
            </div>
            
            <div class="feature-item">
                <i class="bi bi-calendar-event feature-icon"></i>
                <span>Browse previously loaded events</span>
            </div>
            
            <div class="feature-item">
                <i class="bi bi-gift feature-icon"></i>
                <span>View birthday templates</span>
            </div>
            
            <div class="feature-item">
                <i class="bi bi-gear feature-icon"></i>
                <span>Access settings page</span>
            </div>
        </div>
        
        <div class="mt-3">
            <small class="text-muted">
                <i class="bi bi-info-circle"></i> 
                Your actions will be saved and synced when you're back online.
            </small>
        </div>
    </div>
    
    <script>
        // Check connection status
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connectionStatus');
            
            if (navigator.onLine) {
                statusElement.className = 'connection-status status-online';
                statusElement.innerHTML = '<i class="bi bi-wifi"></i> Online';
                
                // Auto-redirect when back online
                setTimeout(() => {
                    window.location.href = 'dashboard.php';
                }, 2000);
            } else {
                statusElement.className = 'connection-status status-offline';
                statusElement.innerHTML = '<i class="bi bi-wifi-off"></i> Offline';
            }
        }
        
        // Retry connection
        function retryConnection() {
            const button = document.querySelector('.retry-btn');
            const originalText = button.innerHTML;
            
            button.innerHTML = '<i class="bi bi-arrow-clockwise"></i> Checking...';
            button.style.opacity = '0.7';
            
            // Check if we're back online
            if (navigator.onLine) {
                button.innerHTML = '<i class="bi bi-check-circle"></i> Connected!';
                button.style.background = '#28a745';
                
                setTimeout(() => {
                    window.location.href = 'dashboard.php';
                }, 1000);
            } else {
                // Try to fetch a small resource to test connection
                fetch('/manifest.json', { 
                    method: 'HEAD',
                    cache: 'no-cache'
                })
                .then(() => {
                    button.innerHTML = '<i class="bi bi-check-circle"></i> Connected!';
                    button.style.background = '#28a745';
                    
                    setTimeout(() => {
                        window.location.href = 'dashboard.php';
                    }, 1000);
                })
                .catch(() => {
                    button.innerHTML = '<i class="bi bi-x-circle"></i> Still Offline';
                    button.style.background = '#dc3545';
                    
                    setTimeout(() => {
                        button.innerHTML = originalText;
                        button.style.background = '';
                        button.style.opacity = '1';
                    }, 2000);
                });
            }
        }
        
        // Listen for connection changes
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);
        
        // Initial status check
        updateConnectionStatus();
        
        // Periodic connection check
        setInterval(updateConnectionStatus, 5000);
        
        // Add keyboard shortcut for retry
        document.addEventListener('keydown', function(e) {
            if (e.key === 'r' && (e.ctrlKey || e.metaKey)) {
                e.preventDefault();
                retryConnection();
            }
        });
        
        // Add swipe down to refresh on mobile
        let startY = 0;
        
        document.addEventListener('touchstart', function(e) {
            startY = e.touches[0].clientY;
        });
        
        document.addEventListener('touchend', function(e) {
            const endY = e.changedTouches[0].clientY;
            const diff = endY - startY;
            
            if (diff > 100 && window.scrollY === 0) {
                retryConnection();
            }
        });
    </script>
</body>
</html>
