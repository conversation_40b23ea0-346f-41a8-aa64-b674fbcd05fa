<?php
// Debug script to investigate event categories duplicates
require_once 'config.php';

echo "<h1>Event Categories Debug Report</h1>";

try {
    // Check if event_categories table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'event_categories'");
    if (!$stmt->fetch()) {
        echo "<p style='color: red;'>❌ event_categories table does not exist!</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ event_categories table exists</p>";
    
    // Get table structure
    echo "<h2>Table Structure</h2>";
    $stmt = $pdo->query("DESCRIBE event_categories");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
    echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>";
    }
    echo "</table><br>";
    
    // Check for unique constraints
    echo "<h2>Indexes and Constraints</h2>";
    $stmt = $pdo->query("SHOW INDEX FROM event_categories");
    $indexes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($indexes)) {
        echo "<p style='color: orange;'>⚠️ No indexes found on event_categories table</p>";
    } else {
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr><th>Key Name</th><th>Column</th><th>Unique</th><th>Type</th></tr>";
        foreach ($indexes as $index) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($index['Key_name']) . "</td>";
            echo "<td>" . htmlspecialchars($index['Column_name']) . "</td>";
            echo "<td>" . ($index['Non_unique'] == 0 ? 'YES' : 'NO') . "</td>";
            echo "<td>" . htmlspecialchars($index['Index_type']) . "</td>";
            echo "</tr>";
        }
        echo "</table><br>";
    }
    
    // Get all categories
    echo "<h2>All Categories (Raw Data)</h2>";
    $stmt = $pdo->query("SELECT * FROM event_categories ORDER BY name, id");
    $all_categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>Total categories: " . count($all_categories) . "</p>";
    
    echo "<table border='1' cellpadding='5' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Description</th><th>Color</th><th>Created At</th><th>Updated At</th></tr>";
    
    foreach ($all_categories as $category) {
        echo "<tr>";
        echo "<td>" . $category['id'] . "</td>";
        echo "<td><strong>" . htmlspecialchars($category['name']) . "</strong></td>";
        echo "<td>" . htmlspecialchars(substr($category['description'] ?? '', 0, 50)) . "...</td>";
        echo "<td style='background-color: " . htmlspecialchars($category['color_code']) . "; color: white;'>" . htmlspecialchars($category['color_code']) . "</td>";
        echo "<td>" . $category['created_at'] . "</td>";
        echo "<td>" . $category['updated_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table><br>";
    
    // Find duplicates by name
    echo "<h2>Duplicate Analysis</h2>";
    $stmt = $pdo->query("
        SELECT name, COUNT(*) as count, 
               GROUP_CONCAT(id ORDER BY id) as ids,
               GROUP_CONCAT(created_at ORDER BY id) as created_dates
        FROM event_categories 
        GROUP BY LOWER(TRIM(name)) 
        HAVING count > 1
        ORDER BY count DESC, name
    ");
    $duplicates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($duplicates)) {
        echo "<p style='color: green;'>✅ No duplicate category names found</p>";
    } else {
        echo "<p style='color: red;'>❌ Found " . count($duplicates) . " sets of duplicate category names:</p>";
        
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Category Name</th><th>Duplicate Count</th><th>IDs</th><th>Created Dates</th></tr>";
        
        foreach ($duplicates as $duplicate) {
            echo "<tr>";
            echo "<td><strong>" . htmlspecialchars($duplicate['name']) . "</strong></td>";
            echo "<td style='color: red;'>" . $duplicate['count'] . "</td>";
            echo "<td>" . htmlspecialchars($duplicate['ids']) . "</td>";
            echo "<td>" . htmlspecialchars($duplicate['created_dates']) . "</td>";
            echo "</tr>";
        }
        echo "</table><br>";
    }
    
    // Check category usage in events
    echo "<h2>Category Usage in Events</h2>";
    $stmt = $pdo->query("
        SELECT ec.id, ec.name, COUNT(e.id) as event_count
        FROM event_categories ec
        LEFT JOIN events e ON ec.id = e.category_id
        GROUP BY ec.id, ec.name
        ORDER BY event_count DESC, ec.name
    ");
    $usage = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
    echo "<tr><th>Category ID</th><th>Category Name</th><th>Events Using This Category</th></tr>";
    
    foreach ($usage as $cat) {
        $bg_color = $cat['event_count'] > 0 ? '#d4edda' : '#f8f9fa';
        echo "<tr style='background-color: " . $bg_color . ";'>";
        echo "<td>" . $cat['id'] . "</td>";
        echo "<td>" . htmlspecialchars($cat['name']) . "</td>";
        echo "<td>" . $cat['event_count'] . "</td>";
        echo "</tr>";
    }
    echo "</table><br>";
    
    // Check for orphaned events (events with invalid category_id)
    echo "<h2>Data Integrity Check</h2>";
    $stmt = $pdo->query("
        SELECT e.id, e.title, e.category_id
        FROM events e
        LEFT JOIN event_categories ec ON e.category_id = ec.id
        WHERE e.category_id IS NOT NULL AND ec.id IS NULL
    ");
    $orphaned = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($orphaned)) {
        echo "<p style='color: green;'>✅ No orphaned events found (all category references are valid)</p>";
    } else {
        echo "<p style='color: red;'>❌ Found " . count($orphaned) . " events with invalid category references:</p>";
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr><th>Event ID</th><th>Event Title</th><th>Invalid Category ID</th></tr>";
        foreach ($orphaned as $event) {
            echo "<tr>";
            echo "<td>" . $event['id'] . "</td>";
            echo "<td>" . htmlspecialchars($event['title']) . "</td>";
            echo "<td style='color: red;'>" . $event['category_id'] . "</td>";
            echo "</tr>";
        }
        echo "</table><br>";
    }
    
    echo "<h2>Recommendations</h2>";
    echo "<ul>";
    if (!empty($duplicates)) {
        echo "<li style='color: red;'>🔧 Remove duplicate categories, keeping the most recent or most used version</li>";
    }
    echo "<li style='color: orange;'>🔧 Add UNIQUE constraint on category name to prevent future duplicates</li>";
    echo "<li style='color: blue;'>🔧 Test delete functionality in admin panel</li>";
    echo "<li style='color: green;'>🔧 Implement client-side validation to prevent duplicate names</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
