<?php
/**
 * Test SMS Integration with Birthday Reminder System
 * 
 * This script tests the integration of SMS functionality with the existing
 * birthday reminder system to ensure both email and SMS notifications work together.
 */

require_once '../config.php';
require_once '../send_birthday_reminders.php';
require_once '../includes/sms_functions.php';

// Set headers for proper output
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SMS Birthday Integration Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h2><i class="bi bi-chat-text"></i> SMS Birthday Integration Test</h2>
                <p class="text-muted">Testing SMS integration with the birthday reminder system</p>
                
                <?php
                try {
                    echo "<div class='card mb-4'>";
                    echo "<div class='card-header'><h5><i class='bi bi-1-circle'></i> System Configuration Check</h5></div>";
                    echo "<div class='card-body'>";
                    
                    // Test 1: Check SMS Manager availability
                    echo "<h6>SMS Manager Configuration:</h6>";
                    $smsManager = getSMSManager();
                    if ($smsManager) {
                        echo "<div class='alert alert-success'><i class='bi bi-check-circle'></i> SMS Manager initialized successfully</div>";
                    } else {
                        echo "<div class='alert alert-warning'><i class='bi bi-exclamation-triangle'></i> SMS Manager not available (this is normal if SMS providers are not configured)</div>";
                    }
                    
                    // Test 2: Check database columns
                    echo "<h6>Database Structure Check:</h6>";
                    $stmt = $pdo->prepare("SHOW COLUMNS FROM members LIKE 'phone_number'");
                    $stmt->execute();
                    $phoneColumn = $stmt->fetch();
                    
                    $stmt = $pdo->prepare("SHOW COLUMNS FROM members LIKE 'sms_notifications'");
                    $stmt->execute();
                    $smsColumn = $stmt->fetch();
                    
                    $stmt = $pdo->prepare("SHOW COLUMNS FROM members LIKE 'phone_verified'");
                    $stmt->execute();
                    $verifiedColumn = $stmt->fetch();
                    
                    if ($phoneColumn) {
                        echo "<div class='alert alert-success'><i class='bi bi-check-circle'></i> phone_number column exists</div>";
                    } else {
                        echo "<div class='alert alert-danger'><i class='bi bi-x-circle'></i> phone_number column missing</div>";
                    }
                    
                    if ($smsColumn) {
                        echo "<div class='alert alert-success'><i class='bi bi-check-circle'></i> sms_notifications column exists</div>";
                    } else {
                        echo "<div class='alert alert-danger'><i class='bi bi-x-circle'></i> sms_notifications column missing</div>";
                    }
                    
                    if ($verifiedColumn) {
                        echo "<div class='alert alert-success'><i class='bi bi-check-circle'></i> phone_verified column exists</div>";
                    } else {
                        echo "<div class='alert alert-danger'><i class='bi bi-x-circle'></i> phone_verified column missing</div>";
                    }
                    
                    echo "</div></div>";
                    
                    // Test 3: Check members with phone numbers
                    echo "<div class='card mb-4'>";
                    echo "<div class='card-header'><h5><i class='bi bi-2-circle'></i> Members Data Analysis</h5></div>";
                    echo "<div class='card-body'>";
                    
                    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM members WHERE status = 'active'");
                    $stmt->execute();
                    $totalMembers = $stmt->fetch()['total'];
                    
                    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM members WHERE phone_number IS NOT NULL AND phone_number != '' AND status = 'active'");
                    $stmt->execute();
                    $membersWithPhone = $stmt->fetch()['total'];
                    
                    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM members WHERE sms_notifications = 1 AND status = 'active'");
                    $stmt->execute();
                    $membersWithSMS = $stmt->fetch()['total'];
                    
                    echo "<div class='row'>";
                    echo "<div class='col-md-4'>";
                    echo "<div class='card bg-primary text-white'>";
                    echo "<div class='card-body text-center'>";
                    echo "<h3>$totalMembers</h3>";
                    echo "<p>Total Active Members</p>";
                    echo "</div></div></div>";
                    
                    echo "<div class='col-md-4'>";
                    echo "<div class='card bg-info text-white'>";
                    echo "<div class='card-body text-center'>";
                    echo "<h3>$membersWithPhone</h3>";
                    echo "<p>Members with Phone Numbers</p>";
                    echo "</div></div></div>";
                    
                    echo "<div class='col-md-4'>";
                    echo "<div class='card bg-success text-white'>";
                    echo "<div class='card-body text-center'>";
                    echo "<h3>$membersWithSMS</h3>";
                    echo "<p>SMS Notifications Enabled</p>";
                    echo "</div></div></div>";
                    echo "</div>";
                    
                    echo "</div></div>";
                    
                    // Test 4: Birthday Reminder System Test
                    echo "<div class='card mb-4'>";
                    echo "<div class='card-header'><h5><i class='bi bi-3-circle'></i> Birthday Reminder System Test</h5></div>";
                    echo "<div class='card-body'>";
                    
                    // Initialize birthday reminder system
                    $birthdayReminder = new BirthdayReminder($pdo);
                    
                    // Check for upcoming birthdays
                    $todayBirthdays = $birthdayReminder->getUpcomingBirthdays(0);
                    $upcomingBirthdays = $birthdayReminder->getUpcomingBirthdays(3);
                    
                    echo "<h6>Birthday Analysis:</h6>";
                    echo "<div class='alert alert-info'>";
                    echo "<strong>Today's Birthdays:</strong> " . count($todayBirthdays) . " members<br>";
                    echo "<strong>Upcoming Birthdays (3 days):</strong> " . count($upcomingBirthdays) . " members";
                    echo "</div>";
                    
                    if (count($todayBirthdays) > 0) {
                        echo "<h6>Today's Birthday Members:</h6>";
                        echo "<div class='table-responsive'>";
                        echo "<table class='table table-sm'>";
                        echo "<thead><tr><th>Name</th><th>Email</th><th>Phone</th><th>SMS Enabled</th></tr></thead>";
                        echo "<tbody>";
                        foreach ($todayBirthdays as $member) {
                            $smsEnabled = isset($member['sms_notifications']) ? ($member['sms_notifications'] ? 'Yes' : 'No') : 'Unknown';
                            echo "<tr>";
                            echo "<td>" . htmlspecialchars($member['full_name']) . "</td>";
                            echo "<td>" . htmlspecialchars($member['email']) . "</td>";
                            echo "<td>" . htmlspecialchars($member['phone_number'] ?? 'N/A') . "</td>";
                            echo "<td>" . $smsEnabled . "</td>";
                            echo "</tr>";
                        }
                        echo "</tbody></table>";
                        echo "</div>";
                    }
                    
                    echo "</div></div>";
                    
                    // Test 5: SMS Templates Check
                    echo "<div class='card mb-4'>";
                    echo "<div class='card-header'><h5><i class='bi bi-4-circle'></i> SMS Templates Check</h5></div>";
                    echo "<div class='card-body'>";
                    
                    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM sms_templates WHERE template_type = 'birthday'");
                    $stmt->execute();
                    $birthdayTemplates = $stmt->fetch()['total'];
                    
                    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM sms_templates WHERE template_type = 'event_reminder'");
                    $stmt->execute();
                    $eventTemplates = $stmt->fetch()['total'];
                    
                    echo "<div class='row'>";
                    echo "<div class='col-md-6'>";
                    echo "<div class='card bg-warning text-dark'>";
                    echo "<div class='card-body text-center'>";
                    echo "<h3>$birthdayTemplates</h3>";
                    echo "<p>Birthday SMS Templates</p>";
                    echo "</div></div></div>";
                    
                    echo "<div class='col-md-6'>";
                    echo "<div class='card bg-secondary text-white'>";
                    echo "<div class='card-body text-center'>";
                    echo "<h3>$eventTemplates</h3>";
                    echo "<p>Event Reminder SMS Templates</p>";
                    echo "</div></div></div>";
                    echo "</div>";
                    
                    echo "</div></div>";
                    
                    // Test 6: Integration Test Summary
                    echo "<div class='card mb-4'>";
                    echo "<div class='card-header'><h5><i class='bi bi-5-circle'></i> Integration Test Summary</h5></div>";
                    echo "<div class='card-body'>";
                    
                    $integrationScore = 0;
                    $maxScore = 6;
                    
                    // Check each component
                    if ($smsManager) $integrationScore++;
                    if ($phoneColumn && $smsColumn && $verifiedColumn) $integrationScore++;
                    if ($membersWithPhone > 0) $integrationScore++;
                    if ($birthdayTemplates > 0) $integrationScore++;
                    if ($eventTemplates > 0) $integrationScore++;
                    if (class_exists('BirthdayReminder')) $integrationScore++;
                    
                    $percentage = round(($integrationScore / $maxScore) * 100);
                    $badgeClass = $percentage >= 80 ? 'success' : ($percentage >= 60 ? 'warning' : 'danger');
                    
                    echo "<div class='alert alert-$badgeClass'>";
                    echo "<h4><i class='bi bi-speedometer2'></i> Integration Score: $integrationScore/$maxScore ($percentage%)</h4>";
                    
                    if ($percentage >= 80) {
                        echo "<p><strong>Excellent!</strong> SMS integration with birthday reminders is ready for production use.</p>";
                    } elseif ($percentage >= 60) {
                        echo "<p><strong>Good!</strong> SMS integration is mostly ready, but some components may need configuration.</p>";
                    } else {
                        echo "<p><strong>Needs Work!</strong> Several components require setup before SMS integration can be used.</p>";
                    }
                    echo "</div>";
                    
                    echo "</div></div>";
                    
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>";
                    echo "<h5><i class='bi bi-exclamation-triangle'></i> Test Error</h5>";
                    echo "<p>Error during testing: " . htmlspecialchars($e->getMessage()) . "</p>";
                    echo "</div>";
                }
                ?>
                
                <div class="mt-4">
                    <a href="sms_campaigns.php" class="btn btn-primary">
                        <i class="bi bi-arrow-right"></i> Go to SMS Campaigns
                    </a>
                    <a href="sms_analytics.php" class="btn btn-secondary">
                        <i class="bi bi-graph-up"></i> View SMS Analytics
                    </a>
                    <a href="../cron/birthday_reminders.php?test=1" class="btn btn-info" target="_blank">
                        <i class="bi bi-play"></i> Test Birthday System
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
