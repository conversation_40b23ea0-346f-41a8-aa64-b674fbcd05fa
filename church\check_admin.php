<?php
require_once 'config.php';

echo "<h1>Admin Credentials Check</h1>";

try {
    $stmt = $pdo->query("SELECT id, username, password, full_name FROM admins");
    $admins = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Current Admin Users:</h2>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Username</th><th>Password Hash</th><th>Full Name</th></tr>";
    
    foreach ($admins as $admin) {
        echo "<tr>";
        echo "<td>" . $admin['id'] . "</td>";
        echo "<td>" . $admin['username'] . "</td>";
        echo "<td>" . $admin['password'] . "</td>";
        echo "<td>" . $admin['full_name'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>Password Hash Tests:</h2>";
    $testPasswords = ['admin123', 'admin', 'password', 'church123', 'freedom123', 'admin@123'];

    foreach ($testPasswords as $testPassword) {
        echo "<h3>Testing password: '$testPassword'</h3>";

        // Test if the stored password matches any of these
        foreach ($admins as $admin) {
            echo "<p><strong>Admin: {$admin['username']}</strong></p>";
            echo "<p>password_verify match: " . (password_verify($testPassword, $admin['password']) ? '<span style="color: green;">YES</span>' : '<span style="color: red;">NO</span>') . "</p>";
        }
        echo "<hr>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
