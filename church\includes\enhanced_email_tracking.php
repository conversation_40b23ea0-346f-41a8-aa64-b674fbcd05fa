<?php
/**
 * Enhanced Email Tracking Functions
 * 
 * This file provides comprehensive email tracking functionality including:
 * - Click tracking with detailed analytics
 * - Engagement scoring
 * - Device and location detection
 * - Advanced metrics calculation
 */

require_once 'email_functions.php';

/**
 * Enhanced tracking pixel with device detection
 */
function getEnhancedTrackingPixel($trackingId, $emailType = 'general', $memberId = null) {
    $baseUrl = rtrim(SITE_URL, '/');
    $trackingUrl = $baseUrl . '/track_enhanced.php?id=' . urlencode($trackingId) . 
                   '&type=open&email_type=' . urlencode($emailType);
    
    if ($memberId) {
        $trackingUrl .= '&member_id=' . intval($memberId);
    }
    
    return '<img src="' . $trackingUrl . '" width="1" height="1" alt="" style="display:none;" />';
}

/**
 * Create trackable links for email content
 */
function createTrackableLink($url, $trackingId, $linkText = null, $emailType = 'general') {
    $baseUrl = rtrim(SITE_URL, '/');
    $trackingUrl = $baseUrl . '/track_enhanced.php?id=' . urlencode($trackingId) . 
                   '&type=click&url=' . urlencode($url) . '&email_type=' . urlencode($emailType);
    
    $displayText = $linkText ?: $url;
    return '<a href="' . $trackingUrl . '" target="_blank">' . htmlspecialchars($displayText) . '</a>';
}

/**
 * Calculate engagement score based on email interactions
 */
function calculateEngagementScore($memberId, $emailType = null, $days = 30) {
    global $pdo;
    
    try {
        $whereClause = "WHERE member_id = ?";
        $params = [$memberId];
        
        if ($emailType) {
            $whereClause .= " AND email_type = ?";
            $params[] = $emailType;
        }
        
        $whereClause .= " AND sent_at >= DATE_SUB(NOW(), INTERVAL ? DAY)";
        $params[] = $days;
        
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total_emails,
                SUM(CASE WHEN opened_at IS NOT NULL THEN 1 ELSE 0 END) as opens,
                SUM(CASE WHEN clicked_at IS NOT NULL THEN 1 ELSE 0 END) as clicks,
                SUM(opened_count) as total_opens,
                SUM(clicked_count) as total_clicks,
                AVG(CASE WHEN opened_at IS NOT NULL THEN 
                    TIMESTAMPDIFF(MINUTE, sent_at, opened_at) ELSE NULL END) as avg_open_time
            FROM email_tracking 
            $whereClause
        ");
        
        $stmt->execute($params);
        $stats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($stats['total_emails'] == 0) {
            return 0.00;
        }
        
        // Calculate engagement score (0-100)
        $openRate = ($stats['opens'] / $stats['total_emails']) * 100;
        $clickRate = ($stats['clicks'] / $stats['total_emails']) * 100;
        $repeatEngagement = ($stats['total_opens'] + $stats['total_clicks']) / $stats['total_emails'];
        
        // Weight factors: open rate (40%), click rate (40%), repeat engagement (20%)
        $engagementScore = ($openRate * 0.4) + ($clickRate * 0.4) + ($repeatEngagement * 0.2);
        
        return min(100.00, round($engagementScore, 2));
        
    } catch (Exception $e) {
        error_log("Error calculating engagement score: " . $e->getMessage());
        return 0.00;
    }
}

/**
 * Update engagement scores for all members
 */
function updateAllEngagementScores() {
    global $pdo;
    
    try {
        // Get all members with email tracking data
        $stmt = $pdo->prepare("
            SELECT DISTINCT member_id 
            FROM email_tracking 
            WHERE sent_at >= DATE_SUB(NOW(), INTERVAL 90 DAY)
        ");
        $stmt->execute();
        $members = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $updateStmt = $pdo->prepare("
            UPDATE email_tracking 
            SET engagement_score = ? 
            WHERE member_id = ? AND sent_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        ");
        
        foreach ($members as $memberId) {
            $score = calculateEngagementScore($memberId);
            $updateStmt->execute([$score, $memberId]);
        }
        
        return count($members);
        
    } catch (Exception $e) {
        error_log("Error updating engagement scores: " . $e->getMessage());
        return 0;
    }
}

/**
 * Get email analytics summary for a date range
 */
function getEmailAnalyticsSummary($startDate, $endDate, $emailType = null) {
    global $pdo;
    
    try {
        $whereClause = "WHERE el.sent_at BETWEEN ? AND ?";
        $params = [$startDate, $endDate];
        
        if ($emailType && $emailType !== 'all') {
            $whereClause .= " AND el.email_type = ?";
            $params[] = $emailType;
        }
        
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(DISTINCT el.id) as total_sent,
                COUNT(DISTINCT CASE WHEN el.status = 'success' THEN el.id END) as delivered,
                COUNT(DISTINCT CASE WHEN et.opened_at IS NOT NULL THEN et.id END) as opened,
                COUNT(DISTINCT CASE WHEN et.clicked_at IS NOT NULL THEN et.id END) as clicked,
                COUNT(DISTINCT CASE WHEN et.bounce_type != 'none' THEN et.id END) as bounced,
                COUNT(DISTINCT CASE WHEN et.unsubscribed_at IS NOT NULL THEN et.id END) as unsubscribed,
                SUM(et.opened_count) as total_opens,
                SUM(et.clicked_count) as total_clicks,
                AVG(et.engagement_score) as avg_engagement_score,
                COUNT(DISTINCT el.member_id) as unique_recipients
            FROM email_logs el
            LEFT JOIN email_tracking et ON el.member_id = et.member_id 
                AND DATE(el.sent_at) = DATE(et.sent_at)
            $whereClause
        ");
        
        $stmt->execute($params);
        $summary = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Calculate rates
        $delivered = max(1, $summary['delivered']); // Avoid division by zero
        $summary['open_rate'] = round(($summary['opened'] / $delivered) * 100, 2);
        $summary['click_rate'] = round(($summary['clicked'] / $delivered) * 100, 2);
        $summary['bounce_rate'] = round(($summary['bounced'] / $summary['total_sent']) * 100, 2);
        $summary['unsubscribe_rate'] = round(($summary['unsubscribed'] / $delivered) * 100, 2);
        $summary['click_to_open_rate'] = $summary['opened'] > 0 ? 
            round(($summary['clicked'] / $summary['opened']) * 100, 2) : 0;
        
        return $summary;
        
    } catch (Exception $e) {
        error_log("Error getting email analytics summary: " . $e->getMessage());
        return [
            'total_sent' => 0, 'delivered' => 0, 'opened' => 0, 'clicked' => 0,
            'bounced' => 0, 'unsubscribed' => 0, 'total_opens' => 0, 'total_clicks' => 0,
            'avg_engagement_score' => 0, 'unique_recipients' => 0,
            'open_rate' => 0, 'click_rate' => 0, 'bounce_rate' => 0,
            'unsubscribe_rate' => 0, 'click_to_open_rate' => 0
        ];
    }
}

/**
 * Get top performing email templates
 */
function getTopPerformingTemplates($limit = 10, $days = 30) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT 
                et.id,
                et.template_name,
                et.template_category,
                COUNT(DISTINCT el.id) as emails_sent,
                COUNT(DISTINCT CASE WHEN etr.opened_at IS NOT NULL THEN etr.id END) as emails_opened,
                COUNT(DISTINCT CASE WHEN etr.clicked_at IS NOT NULL THEN etr.id END) as emails_clicked,
                ROUND(COUNT(DISTINCT CASE WHEN etr.opened_at IS NOT NULL THEN etr.id END) / 
                      COUNT(DISTINCT el.id) * 100, 2) as open_rate,
                ROUND(COUNT(DISTINCT CASE WHEN etr.clicked_at IS NOT NULL THEN etr.id END) / 
                      COUNT(DISTINCT el.id) * 100, 2) as click_rate,
                AVG(etr.engagement_score) as avg_engagement_score
            FROM email_templates et
            LEFT JOIN email_logs el ON et.id = el.template_id
            LEFT JOIN email_tracking etr ON el.member_id = etr.member_id 
                AND DATE(el.sent_at) = DATE(etr.sent_at)
            WHERE el.sent_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            GROUP BY et.id, et.template_name, et.template_category
            HAVING emails_sent > 0
            ORDER BY avg_engagement_score DESC, open_rate DESC, click_rate DESC
            LIMIT ?
        ");
        
        $stmt->execute([$days, $limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("Error getting top performing templates: " . $e->getMessage());
        return [];
    }
}

/**
 * Get device and location analytics
 */
function getDeviceLocationAnalytics($days = 30) {
    global $pdo;
    
    try {
        // Device analytics
        $deviceStmt = $pdo->prepare("
            SELECT 
                device_type,
                COUNT(*) as count,
                COUNT(CASE WHEN opened_at IS NOT NULL THEN 1 END) as opens,
                COUNT(CASE WHEN clicked_at IS NOT NULL THEN 1 END) as clicks
            FROM email_tracking 
            WHERE sent_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
                AND device_type IS NOT NULL
            GROUP BY device_type
            ORDER BY count DESC
        ");
        $deviceStmt->execute([$days]);
        $deviceStats = $deviceStmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Location analytics
        $locationStmt = $pdo->prepare("
            SELECT 
                location_country,
                location_city,
                COUNT(*) as count,
                COUNT(CASE WHEN opened_at IS NOT NULL THEN 1 END) as opens,
                COUNT(CASE WHEN clicked_at IS NOT NULL THEN 1 END) as clicks
            FROM email_tracking 
            WHERE sent_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
                AND location_country IS NOT NULL
            GROUP BY location_country, location_city
            ORDER BY count DESC
            LIMIT 20
        ");
        $locationStmt->execute([$days]);
        $locationStats = $locationStmt->fetchAll(PDO::FETCH_ASSOC);
        
        return [
            'devices' => $deviceStats,
            'locations' => $locationStats
        ];
        
    } catch (Exception $e) {
        error_log("Error getting device/location analytics: " . $e->getMessage());
        return ['devices' => [], 'locations' => []];
    }
}

/**
 * Detect device type from user agent
 */
function detectDeviceType($userAgent) {
    $userAgent = strtolower($userAgent);
    
    if (strpos($userAgent, 'mobile') !== false || strpos($userAgent, 'android') !== false) {
        return 'Mobile';
    } elseif (strpos($userAgent, 'tablet') !== false || strpos($userAgent, 'ipad') !== false) {
        return 'Tablet';
    } else {
        return 'Desktop';
    }
}

/**
 * Get approximate location from IP address (basic implementation)
 */
function getLocationFromIP($ipAddress) {
    // This is a basic implementation. For production, consider using a proper IP geolocation service
    if ($ipAddress === '127.0.0.1' || $ipAddress === '::1') {
        return ['country' => 'Local', 'city' => 'Localhost'];
    }
    
    // For now, return unknown. In production, integrate with IP geolocation API
    return ['country' => 'Unknown', 'city' => 'Unknown'];
}
?>
