<?php
// Cleanup script for duplicate event categories
require_once 'config.php';

echo "<h1>Event Categories Cleanup Script</h1>";

// Check if this is a POST request to execute cleanup
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['execute_cleanup'])) {
    echo "<h2>🔧 Executing Cleanup...</h2>";
    
    try {
        $pdo->beginTransaction();
        
        // Get all duplicate category groups
        $stmt = $pdo->query("
            SELECT name, 
                   GROUP_CONCAT(id ORDER BY created_at DESC, id DESC) as ids,
                   COUNT(*) as count
            FROM event_categories 
            GROUP BY LOWER(TRIM(name)) 
            HAVING count > 1
            ORDER BY count DESC, name
        ");
        $duplicateGroups = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $totalDeleted = 0;
        $totalUpdated = 0;
        
        foreach ($duplicateGroups as $group) {
            $ids = explode(',', $group['ids']);
            $keepId = array_shift($ids); // Keep the first one (most recent)
            $deleteIds = $ids; // Delete the rest
            
            echo "<div class='alert alert-info'>";
            echo "<strong>Processing: {$group['name']}</strong><br>";
            echo "Keeping ID: $keepId (most recent)<br>";
            echo "Deleting IDs: " . implode(', ', $deleteIds) . "<br>";
            
            // Update any events that reference the categories we're about to delete
            if (!empty($deleteIds)) {
                $placeholders = str_repeat('?,', count($deleteIds) - 1) . '?';
                $updateStmt = $pdo->prepare("
                    UPDATE events 
                    SET category_id = ? 
                    WHERE category_id IN ($placeholders)
                ");
                $params = array_merge([$keepId], $deleteIds);
                $updateStmt->execute($params);
                $eventsUpdated = $updateStmt->rowCount();
                $totalUpdated += $eventsUpdated;
                
                if ($eventsUpdated > 0) {
                    echo "Updated $eventsUpdated events to use category ID $keepId<br>";
                }
                
                // Delete the duplicate categories
                $deleteStmt = $pdo->prepare("
                    DELETE FROM event_categories 
                    WHERE id IN ($placeholders)
                ");
                $deleteStmt->execute($deleteIds);
                $categoriesDeleted = $deleteStmt->rowCount();
                $totalDeleted += $categoriesDeleted;
                
                echo "Deleted $categoriesDeleted duplicate categories<br>";
            }
            echo "</div>";
        }
        
        // Add unique constraint to prevent future duplicates
        echo "<div class='alert alert-warning'>";
        echo "<strong>Adding UNIQUE constraint...</strong><br>";
        try {
            $pdo->exec("ALTER TABLE event_categories ADD UNIQUE KEY unique_name (name)");
            echo "✅ Successfully added UNIQUE constraint on category name<br>";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                echo "⚠️ UNIQUE constraint already exists<br>";
            } else {
                echo "❌ Error adding UNIQUE constraint: " . $e->getMessage() . "<br>";
            }
        }
        echo "</div>";
        
        $pdo->commit();
        
        echo "<div class='alert alert-success'>";
        echo "<h3>✅ Cleanup Completed Successfully!</h3>";
        echo "Total categories deleted: $totalDeleted<br>";
        echo "Total events updated: $totalUpdated<br>";
        echo "</div>";
        
        // Show final stats
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM event_categories");
        $finalCount = $stmt->fetch()['total'];
        
        $stmt = $pdo->query("
            SELECT COUNT(*) as duplicates 
            FROM (
                SELECT name, COUNT(*) as count
                FROM event_categories 
                GROUP BY LOWER(TRIM(name)) 
                HAVING count > 1
            ) as dup_check
        ");
        $remainingDuplicates = $stmt->fetch()['duplicates'];
        
        echo "<div class='alert alert-info'>";
        echo "<h4>Final Statistics:</h4>";
        echo "Total categories remaining: $finalCount<br>";
        echo "Duplicate category names remaining: $remainingDuplicates<br>";
        echo "</div>";
        
    } catch (Exception $e) {
        $pdo->rollBack();
        echo "<div class='alert alert-danger'>";
        echo "<h3>❌ Error during cleanup:</h3>";
        echo $e->getMessage();
        echo "</div>";
    }
} else {
    // Show preview of what will be cleaned up
    echo "<h2>📋 Cleanup Preview</h2>";
    
    // Get duplicate statistics
    $stmt = $pdo->query("
        SELECT name, 
               COUNT(*) as count,
               GROUP_CONCAT(id ORDER BY created_at DESC, id DESC) as ids,
               GROUP_CONCAT(created_at ORDER BY created_at DESC, id DESC) as dates
        FROM event_categories 
        GROUP BY LOWER(TRIM(name)) 
        HAVING count > 1
        ORDER BY count DESC, name
    ");
    $duplicates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($duplicates)) {
        echo "<div class='alert alert-success'>";
        echo "<h3>✅ No duplicates found!</h3>";
        echo "All category names are unique.";
        echo "</div>";
    } else {
        echo "<div class='alert alert-warning'>";
        echo "<h3>⚠️ Found " . count($duplicates) . " sets of duplicate category names</h3>";
        echo "This script will:</p>";
        echo "<ul>";
        echo "<li>Keep the most recent version of each duplicate category</li>";
        echo "<li>Update any events using duplicate categories to reference the kept version</li>";
        echo "<li>Delete all duplicate category entries</li>";
        echo "<li>Add a UNIQUE constraint to prevent future duplicates</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<table class='table table-bordered'>";
        echo "<thead>";
        echo "<tr><th>Category Name</th><th>Duplicates</th><th>Will Keep ID</th><th>Will Delete IDs</th></tr>";
        echo "</thead>";
        echo "<tbody>";
        
        $totalToDelete = 0;
        foreach ($duplicates as $dup) {
            $ids = explode(',', $dup['ids']);
            $keepId = array_shift($ids);
            $deleteIds = $ids;
            $totalToDelete += count($deleteIds);
            
            echo "<tr>";
            echo "<td><strong>" . htmlspecialchars($dup['name']) . "</strong></td>";
            echo "<td>" . $dup['count'] . "</td>";
            echo "<td class='text-success'>" . $keepId . "</td>";
            echo "<td class='text-danger'>" . implode(', ', $deleteIds) . "</td>";
            echo "</tr>";
        }
        echo "</tbody>";
        echo "</table>";
        
        echo "<div class='alert alert-info'>";
        echo "<h4>Summary:</h4>";
        echo "Total duplicate categories to delete: <strong>$totalToDelete</strong><br>";
        echo "Unique categories that will remain: <strong>" . count($duplicates) . "</strong><br>";
        echo "</div>";
        
        // Show form to execute cleanup
        echo "<form method='POST' onsubmit='return confirm(\"Are you sure you want to execute the cleanup? This action cannot be undone!\")'>";
        echo "<input type='hidden' name='execute_cleanup' value='1'>";
        echo "<button type='submit' class='btn btn-danger btn-lg'>🔧 Execute Cleanup</button>";
        echo "</form>";
    }
}

// Add some basic styling
echo "<style>
.alert { padding: 15px; margin: 10px 0; border-radius: 5px; }
.alert-success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
.alert-warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
.alert-danger { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
.alert-info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
.table { width: 100%; border-collapse: collapse; margin: 20px 0; }
.table th, .table td { padding: 8px; border: 1px solid #ddd; text-align: left; }
.table thead { background-color: #f8f9fa; }
.btn { padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
.btn-danger { background-color: #dc3545; color: white; }
.btn-lg { padding: 15px 30px; font-size: 18px; }
.text-success { color: #28a745; }
.text-danger { color: #dc3545; }
</style>";
?>
