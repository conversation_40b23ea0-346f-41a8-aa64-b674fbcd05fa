[2025-03-29 08:15:06] Raw schedules count: 2
Schedule IDs: 17, 14

[2025-03-29 08:15:07] Raw schedules count: 2
Schedule IDs: 17, 14

[2025-03-29 08:15:08] Raw schedules count: 2
Schedule IDs: 17, 14

[2025-03-29 08:17:37] Raw schedules count before deduplication: 2
After deduplication: 2
Schedule IDs: 17, 14

[2025-03-29 08:17:45] Raw schedules count before deduplication: 2
After deduplication: 2
Schedule IDs: 17, 14

[2025-03-29 08:24:58] Raw schedules count before deduplication: 2
After deduplication: 2
Schedule IDs: 17, 14
Schedule Statuses: active, active

[2025-03-29 08:25:49] Raw schedules count before deduplication: 2
After deduplication: 2
Schedule IDs: 17, 14
Schedule Statuses: active, active

[2025-03-29 08:31:02] Schedules count: 2
Schedule IDs: 17, 14

[2025-03-29 08:31:19] Schedules count: 2
Schedule IDs: 17, 14

[2025-03-29 08:31:21] Schedules count: 2
Schedule IDs: 17, 14

[2025-03-29 08:36:50] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id) as total_recipients,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent') as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id AND ess.id = (
            SELECT MIN(id) FROM email_schedule_settings WHERE schedule_id = es.id
        )
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 08:36:50] Schedules count: 2
Schedule IDs: 17, 14
Schedule Names: Rhonda, Godwin Bointa
Schedule Statuses: active, active

[2025-03-29 08:37:54] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id) as total_recipients,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent') as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id AND ess.id = (
            SELECT MIN(id) FROM email_schedule_settings WHERE schedule_id = es.id
        )
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 08:37:54] Schedules count: 2
Schedule IDs: 17, 14
Schedule Names: Rhonda, Godwin Bointa
Schedule Statuses: active, active

[2025-03-29 08:39:01] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id) as total_recipients,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent') as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 08:39:01] Schedules count: 2
Schedule IDs: 17, 14
Schedule Names: Rhonda, Godwin Bointa
Schedule Statuses: active, active

[2025-03-29 08:39:09] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id) as total_recipients,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent') as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 08:39:09] Schedules count: 2
Schedule IDs: 17, 14
Schedule Names: Rhonda, Godwin Bointa
Schedule Statuses: active, active

[2025-03-29 08:40:22] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id) as total_recipients,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent') as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 08:40:22] Schedules count: 2
Schedule IDs: 17, 14
Schedule Names: Rhonda, Godwin Bointa
Schedule Statuses: active, active

[2025-03-29 08:41:03] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id) as total_recipients,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent') as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 08:41:03] Schedules count: 2
Schedule IDs: 17, 14
Schedule Names: Rhonda, Godwin Bointa
Schedule Statuses: active, active

[2025-03-29 08:41:36] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id) as total_recipients,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent') as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 08:41:36] Schedules count: 3
Schedule IDs: 18, 17, 14
Schedule Names: rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, active, active

[2025-03-29 08:42:11] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id) as total_recipients,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent') as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 08:42:11] Schedules count: 3
Schedule IDs: 18, 17, 14
Schedule Names: rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, active, active

[2025-03-29 08:42:44] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id) as total_recipients,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent') as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 08:42:44] Schedules count: 4
Schedule IDs: 19, 18, 17, 14
Schedule Names: Mike Rhonda, rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, active, active, active

[2025-03-29 08:43:40] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id) as total_recipients,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent') as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 08:43:40] Schedules count: 4
Schedule IDs: 19, 18, 17, 14
Schedule Names: Mike Rhonda, rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, active, active, active

[2025-03-29 08:43:40] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id) as total_recipients,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent') as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 08:43:40] Schedules count: 4
Schedule IDs: 19, 18, 17, 14
Schedule Names: Mike Rhonda, rhonda, Rhonda, Godwin Bointa
Schedule Statuses: , active, active, active

[2025-03-29 08:44:17] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id) as total_recipients,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent') as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 08:44:17] Schedules count: 4
Schedule IDs: 19, 18, 17, 14
Schedule Names: Mike Rhonda, rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, active, active, active

[2025-03-29 08:45:32] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id) as total_recipients,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent') as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 08:45:32] Schedules count: 4
Schedule IDs: 19, 18, 17, 14
Schedule Names: Mike Rhonda, rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, active, active, active

[2025-03-29 08:45:41] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id) as total_recipients,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent') as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 08:45:41] Schedules count: 3
Schedule IDs: 19, 17, 14
Schedule Names: Mike Rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, active, active

[2025-03-29 08:48:33] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id) as total_recipients,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent') as sent_count,
        (SELECT GROUP_CONCAT(recipient_id) FROM email_schedule_recipients WHERE schedule_id = es.id) as recipient_ids
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 08:48:33] Schedules count: 3
Schedule IDs: 19, 17, 14
Schedule Names: Mike Rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, active, active

[2025-03-29 08:48:33] Adjusted recipient count for schedule ID 19 from 1 to 1
[2025-03-29 08:48:33] Adjusted recipient count for schedule ID 17 from 1 to 1
[2025-03-29 08:48:33] Adjusted recipient count for schedule ID 14 from 1 to 1
[2025-03-29 08:48:33] Adjusted recipient count for schedule ID 19 from 1 to 1
[2025-03-29 08:48:33] Adjusted recipient count for schedule ID 17 from 1 to 1
[2025-03-29 08:48:33] Adjusted recipient count for schedule ID 14 from 1 to 1
[2025-03-29 08:48:59] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id) as total_recipients,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent') as sent_count,
        (SELECT GROUP_CONCAT(recipient_id) FROM email_schedule_recipients WHERE schedule_id = es.id) as recipient_ids
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 08:48:59] Schedules count: 3
Schedule IDs: 19, 17, 14
Schedule Names: Mike Rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, active, active

[2025-03-29 08:48:59] Adjusted recipient count for schedule ID 19 from 1 to 1
[2025-03-29 08:48:59] Adjusted recipient count for schedule ID 17 from 1 to 1
[2025-03-29 08:48:59] Adjusted recipient count for schedule ID 14 from 1 to 1
[2025-03-29 08:48:59] Adjusted recipient count for schedule ID 19 from 1 to 1
[2025-03-29 08:48:59] Adjusted recipient count for schedule ID 17 from 1 to 1
[2025-03-29 08:48:59] Adjusted recipient count for schedule ID 14 from 1 to 1
[2025-03-29 08:49:08] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id) as total_recipients,
        (SELECT COUNT(*) FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent') as sent_count,
        (SELECT GROUP_CONCAT(recipient_id) FROM email_schedule_recipients WHERE schedule_id = es.id) as recipient_ids
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 08:49:08] Schedules count: 3
Schedule IDs: 19, 17, 14
Schedule Names: Mike Rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, active, active

[2025-03-29 08:49:08] Adjusted recipient count for schedule ID 19 from 1 to 1
[2025-03-29 08:49:08] Adjusted recipient count for schedule ID 17 from 1 to 1
[2025-03-29 08:49:08] Adjusted recipient count for schedule ID 14 from 1 to 1
[2025-03-29 08:49:08] Adjusted recipient count for schedule ID 19 from 1 to 1
[2025-03-29 08:49:08] Adjusted recipient count for schedule ID 17 from 1 to 1
[2025-03-29 08:49:08] Adjusted recipient count for schedule ID 14 from 1 to 1
[2025-03-29 08:51:18] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 08:51:18] Schedules count: 3
Schedule IDs: 19, 17, 14
Schedule Names: Mike Rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, active, active

[2025-03-29 08:51:25] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 08:51:25] Schedules count: 3
Schedule IDs: 19, 17, 14
Schedule Names: Mike Rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, active, active

[2025-03-29 08:54:06] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 08:54:06] Schedules count: 3
Schedule IDs: 19, 17, 14
Schedule Names: Mike Rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, active, active

[2025-03-29 08:54:11] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 08:54:11] Schedules count: 3
Schedule IDs: 19, 17, 14
Schedule Names: Mike Rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, active, active

[2025-03-29 08:54:24] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 08:54:24] Schedules count: 3
Schedule IDs: 19, 17, 14
Schedule Names: Mike Rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, active, active

[2025-03-29 08:55:23] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 08:55:23] Schedules count: 3
Schedule IDs: 19, 17, 14
Schedule Names: Mike Rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, active, active

[2025-03-29 08:55:51] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 08:55:51] Schedules count: 3
Schedule IDs: 19, 17, 14
Schedule Names: Mike Rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, active, active

[2025-03-29 08:56:26] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 08:56:26] Schedules count: 3
Schedule IDs: 19, 17, 14
Schedule Names: Mike Rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, active, active

[2025-03-29 09:01:33] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 09:01:33] Schedules count: 3
Schedule IDs: 19, 17, 14
Schedule Names: Mike Rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, active, active

[2025-03-29 09:01:36] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 09:01:36] Schedules count: 3
Schedule IDs: 19, 17, 14
Schedule Names: Mike Rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, active, active

[2025-03-29 09:02:28] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 09:02:28] Schedules count: 3
Schedule IDs: 19, 17, 14
Schedule Names: Mike Rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, active, active

[2025-03-29 09:02:28] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 09:02:28] Schedules count: 3
Schedule IDs: 19, 17, 14
Schedule Names: Mike Rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, , active

[2025-03-29 09:03:22] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 09:03:22] Schedules count: 3
Schedule IDs: 19, 17, 14
Schedule Names: Mike Rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, , active

[2025-03-29 09:04:39] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 09:04:39] Schedules count: 3
Schedule IDs: 19, 17, 14
Schedule Names: Mike Rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, , active

[2025-03-29 09:04:41] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 09:04:41] Schedules count: 3
Schedule IDs: 19, 17, 14
Schedule Names: Mike Rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, , active

[2025-03-29 09:04:57] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 09:04:57] Schedules count: 3
Schedule IDs: 19, 17, 14
Schedule Names: Mike Rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, , active

[2025-03-29 09:05:41] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 09:05:41] Schedules count: 3
Schedule IDs: 19, 17, 14
Schedule Names: Mike Rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, , active

[2025-03-29 09:06:02] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 09:06:02] Schedules count: 3
Schedule IDs: 19, 17, 14
Schedule Names: Mike Rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, , active

[2025-03-29 09:06:26] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 09:06:26] Schedules count: 3
Schedule IDs: 19, 17, 14
Schedule Names: Mike Rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, , active

[2025-03-29 09:07:58] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 09:07:58] Schedules count: 3
Schedule IDs: 19, 17, 14
Schedule Names: Mike Rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, , active

[2025-03-29 09:08:00] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 09:08:00] Schedules count: 3
Schedule IDs: 19, 17, 14
Schedule Names: Mike Rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, , active

[2025-03-29 09:08:14] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 09:08:14] Schedules count: 3
Schedule IDs: 19, 17, 14
Schedule Names: Mike Rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, , active

[2025-03-29 09:09:04] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 09:09:04] Schedules count: 4
Schedule IDs: 20, 19, 17, 14
Schedule Names: Mike Churrh, Mike Rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, active, , active

[2025-03-29 09:09:55] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 09:09:55] Schedules count: 4
Schedule IDs: 20, 19, 17, 14
Schedule Names: Mike Churrh, Mike Rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, active, , active

[2025-03-29 09:10:11] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 09:10:11] Schedules count: 4
Schedule IDs: 20, 19, 17, 14
Schedule Names: Mike Churrh, Mike Rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, active, , active

[2025-03-29 09:10:55] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 09:10:55] Schedules count: 5
Schedule IDs: 21, 20, 19, 17, 14
Schedule Names: Chucker Milion, Mike Churrh, Mike Rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, active, active, , active

[2025-03-29 09:11:50] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 09:11:51] Schedules count: 5
Schedule IDs: 21, 20, 19, 17, 14
Schedule Names: Chucker Milion, Mike Churrh, Mike Rhonda, Rhonda, Godwin Bointa
Schedule Statuses: active, active, active, , active

[2025-03-29 09:12:02] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 09:12:02] Schedules count: 4
Schedule IDs: 21, 20, 19, 14
Schedule Names: Chucker Milion, Mike Churrh, Mike Rhonda, Godwin Bointa
Schedule Statuses: active, active, active, active

[2025-03-29 09:13:20] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 09:13:21] Schedules count: 4
Schedule IDs: 21, 20, 19, 14
Schedule Names: Chucker Milion, Mike Churrh, Mike Rhonda, Godwin Bointa
Schedule Statuses: active, active, active, active

[2025-03-29 09:27:53] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 09:27:53] Schedules count: 4
Schedule IDs: 21, 20, 19, 14
Schedule Names: Chucker Milion, Mike Churrh, Mike Rhonda, Godwin Bointa
Schedule Statuses: , , active, 

[2025-03-29 09:28:15] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 09:28:15] Schedules count: 4
Schedule IDs: 21, 20, 19, 14
Schedule Names: Chucker Milion, Mike Churrh, Mike Rhonda, Godwin Bointa
Schedule Statuses: , , active, 

[2025-03-29 09:28:25] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 09:28:25] Schedules count: 4
Schedule IDs: 21, 20, 19, 14
Schedule Names: Chucker Milion, Mike Churrh, Mike Rhonda, Godwin Bointa
Schedule Statuses: , , active, 

[2025-03-29 09:46:53] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 09:46:53] Schedules count: 4
Schedule IDs: 21, 20, 19, 14
Schedule Names: Chucker Milion, Mike Churrh, Mike Rhonda, Godwin Bointa
Schedule Statuses: , , active, 

[2025-03-29 09:48:48] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 09:48:49] Schedules count: 5
Schedule IDs: 22, 21, 20, 19, 14
Schedule Names: Ndi, Chucker Milion, Mike Churrh, Mike Rhonda, Godwin Bointa
Schedule Statuses: active, , , active, 

[2025-03-29 09:49:46] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 09:49:46] Schedules count: 5
Schedule IDs: 22, 21, 20, 19, 14
Schedule Names: Ndi, Chucker Milion, Mike Churrh, Mike Rhonda, Godwin Bointa
Schedule Statuses: active, , , active, 

[2025-03-29 09:54:27] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 09:54:27] Schedules count: 5
Schedule IDs: 22, 21, 20, 19, 14
Schedule Names: Ndi, Chucker Milion, Mike Churrh, Mike Rhonda, Godwin Bointa
Schedule Statuses: , , , active, 

[2025-03-29 09:55:23] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 09:55:23] Schedules count: 6
Schedule IDs: 23, 22, 21, 20, 19, 14
Schedule Names: Ndibbb, Ndi, Chucker Milion, Mike Churrh, Mike Rhonda, Godwin Bointa
Schedule Statuses: active, , , , active, 

[2025-03-29 09:56:12] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 09:56:12] Schedules count: 6
Schedule IDs: 23, 22, 21, 20, 19, 14
Schedule Names: Ndibbb, Ndi, Chucker Milion, Mike Churrh, Mike Rhonda, Godwin Bointa
Schedule Statuses: active, , , , active, 

[2025-03-29 10:07:46] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:07:47] Schedules count: 6
Schedule IDs: 23, 22, 21, 20, 19, 14
Schedule Names: Ndibbb, Ndi, Chucker Milion, Mike Churrh, Mike Rhonda, Godwin Bointa
Schedule Statuses: active, , , , active, 

[2025-03-29 10:08:42] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:08:42] Schedules count: 7
Schedule IDs: 24, 23, 22, 21, 20, 19, 14
Schedule Names: Ndibbbnnnn, Ndibbb, Ndi, Chucker Milion, Mike Churrh, Mike Rhonda, Godwin Bointa
Schedule Statuses: active, active, , , , active, 

[2025-03-29 10:09:26] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:09:26] Schedules count: 7
Schedule IDs: 24, 23, 22, 21, 20, 19, 14
Schedule Names: Ndibbbnnnn, Ndibbb, Ndi, Chucker Milion, Mike Churrh, Mike Rhonda, Godwin Bointa
Schedule Statuses: active, active, , , , active, 

[2025-03-29 10:09:41] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:09:41] Schedules count: 7
Schedule IDs: 24, 23, 22, 21, 20, 19, 14
Schedule Names: Ndibbbnnnn, Ndibbb, Ndi, Chucker Milion, Mike Churrh, Mike Rhonda, Godwin Bointa
Schedule Statuses: active, active, , , , active, 

[2025-03-29 10:15:15] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:15:15] Schedules count: 8
Schedule IDs: 25, 24, 23, 22, 21, 20, 19, 14
Schedule Names: rhondadd, Ndibbbnnnn, Ndibbb, Ndi, Chucker Milion, Mike Churrh, Mike Rhonda, Godwin Bointa
Schedule Statuses: active, , active, , , , active, 

[2025-03-29 10:16:09] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:16:09] Schedules count: 8
Schedule IDs: 25, 24, 23, 22, 21, 20, 19, 14
Schedule Names: rhondadd, Ndibbbnnnn, Ndibbb, Ndi, Chucker Milion, Mike Churrh, Mike Rhonda, Godwin Bointa
Schedule Statuses: active, , active, , , , active, 

[2025-03-29 10:28:42] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:28:42] Schedules count: 9
Schedule IDs: 26, 25, 24, 23, 22, 21, 20, 19, 14
Schedule Names: Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb, Ndi, Chucker Milion, Mike Churrh, Mike Rhonda, Godwin Bointa
Schedule Statuses: active, active, , active, , , , active, 

[2025-03-29 10:29:11] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:29:11] Schedules count: 10
Schedule IDs: 27, 26, 25, 24, 23, 22, 21, 20, 19, 14
Schedule Names: Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb, Ndi, Chucker Milion, Mike Churrh, Mike Rhonda, Godwin Bointa
Schedule Statuses: active, active, active, , active, , , , active, 

[2025-03-29 10:30:11] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:30:11] Schedules count: 10
Schedule IDs: 27, 26, 25, 24, 23, 22, 21, 20, 19, 14
Schedule Names: Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb, Ndi, Chucker Milion, Mike Churrh, Mike Rhonda, Godwin Bointa
Schedule Statuses: active, active, active, , active, , , , active, 

[2025-03-29 10:30:53] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:30:53] Schedules count: 9
Schedule IDs: 27, 26, 25, 24, 23, 22, 21, 20, 19
Schedule Names: Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb, Ndi, Chucker Milion, Mike Churrh, Mike Rhonda
Schedule Statuses: active, active, active, , active, , , , active

[2025-03-29 10:30:59] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:30:59] Schedules count: 8
Schedule IDs: 27, 26, 25, 24, 23, 22, 21, 20
Schedule Names: Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb, Ndi, Chucker Milion, Mike Churrh
Schedule Statuses: active, active, active, , active, , , 

[2025-03-29 10:31:04] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:31:04] Schedules count: 7
Schedule IDs: 27, 26, 25, 24, 23, 22, 21
Schedule Names: Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb, Ndi, Chucker Milion
Schedule Statuses: active, active, active, , active, , 

[2025-03-29 10:31:09] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:31:09] Schedules count: 6
Schedule IDs: 27, 26, 25, 24, 23, 22
Schedule Names: Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb, Ndi
Schedule Statuses: active, active, active, , active, 

[2025-03-29 10:31:15] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:31:15] Schedules count: 5
Schedule IDs: 27, 26, 25, 24, 23
Schedule Names: Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, , active

[2025-03-29 10:31:39] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:31:39] Schedules count: 6
Schedule IDs: 28, 27, 26, 25, 24, 23
Schedule Names: rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, , active

[2025-03-29 10:35:26] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:35:26] Schedules count: 9
Schedule IDs: 31, 30, 29, 28, 27, 26, 25, 24, 23
Schedule Names: Test Birthday Email Fix, Test Birthday Email Fix, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, , , active, , active

[2025-03-29 10:35:28] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:35:28] Schedules count: 9
Schedule IDs: 31, 30, 29, 28, 27, 26, 25, 24, 23
Schedule Names: Test Birthday Email Fix, Test Birthday Email Fix, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, , , active, , active

[2025-03-29 10:37:13] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:37:13] Schedules count: 9
Schedule IDs: 31, 30, 29, 28, 27, 26, 25, 24, 23
Schedule Names: Test Birthday Email Fix, Test Birthday Email Fix, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, , , active, , active

[2025-03-29 10:38:15] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:38:15] Schedules count: 9
Schedule IDs: 31, 30, 29, 28, 27, 26, 25, 24, 23
Schedule Names: Test Birthday Email Fix, Test Birthday Email Fix, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, , , active, , active

[2025-03-29 10:41:53] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:41:53] Schedules count: 9
Schedule IDs: 31, 30, 29, 28, 27, 26, 25, 24, 23
Schedule Names: Test Birthday Email Fix, Test Birthday Email Fix, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, , , active, , active

[2025-03-29 10:42:00] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:42:00] Schedules count: 9
Schedule IDs: 31, 30, 29, 28, 27, 26, 25, 24, 23
Schedule Names: Test Birthday Email Fix, Test Birthday Email Fix, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, , , active, , active

[2025-03-29 10:42:48] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:42:48] Schedules count: 9
Schedule IDs: 31, 30, 29, 28, 27, 26, 25, 24, 23
Schedule Names: Test Birthday Email Fix, Test Birthday Email Fix, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, , , active, , active

[2025-03-29 10:43:37] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:43:38] Schedules count: 10
Schedule IDs: 32, 31, 30, 29, 28, 27, 26, 25, 24, 23
Schedule Names: tttt, Test Birthday Email Fix, Test Birthday Email Fix, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, active, , , active, , active

[2025-03-29 10:44:13] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:44:13] Schedules count: 11
Schedule IDs: 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23
Schedule Names: ffffff, tttt, Test Birthday Email Fix, Test Birthday Email Fix, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, active, active, , , active, , active

[2025-03-29 10:44:49] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:44:49] Schedules count: 12
Schedule IDs: 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23
Schedule Names: ddddd, ffffff, tttt, Test Birthday Email Fix, Test Birthday Email Fix, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, active, active, active, , , active, , active

[2025-03-29 10:46:35] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:46:35] Schedules count: 14
Schedule IDs: 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23
Schedule Names: Test Birthday Email Names, Test Birthday Email Names, ddddd, ffffff, tttt, Test Birthday Email Fix, Test Birthday Email Fix, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, active, , , , , , , active, , active

[2025-03-29 10:46:53] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:46:53] Schedules count: 13
Schedule IDs: 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23
Schedule Names: Test Birthday Email Names, ddddd, ffffff, tttt, Test Birthday Email Fix, Test Birthday Email Fix, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, , , , , , , active, , active

[2025-03-29 10:46:58] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:46:59] Schedules count: 12
Schedule IDs: 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23
Schedule Names: ddddd, ffffff, tttt, Test Birthday Email Fix, Test Birthday Email Fix, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, , , , , , , active, , active

[2025-03-29 10:47:36] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:47:37] Schedules count: 12
Schedule IDs: 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23
Schedule Names: ddddd, ffffff, tttt, Test Birthday Email Fix, Test Birthday Email Fix, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, , , , , , , active, , active

[2025-03-29 10:50:06] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:50:06] Schedules count: 14
Schedule IDs: 38, 37, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23
Schedule Names: Test Birthday Email Names, Test Birthday Email Names, ddddd, ffffff, tttt, Test Birthday Email Fix, Test Birthday Email Fix, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, , active, active, , , , , , , active, , active

[2025-03-29 10:50:08] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:50:08] Schedules count: 14
Schedule IDs: 38, 37, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23
Schedule Names: Test Birthday Email Names, Test Birthday Email Names, ddddd, ffffff, tttt, Test Birthday Email Fix, Test Birthday Email Fix, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, , active, active, , , , , , , active, , active

[2025-03-29 10:51:18] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:51:18] Schedules count: 14
Schedule IDs: 38, 37, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23
Schedule Names: Test Birthday Email Names, Test Birthday Email Names, ddddd, ffffff, tttt, Test Birthday Email Fix, Test Birthday Email Fix, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, , active, active, , , , , , , active, , active

[2025-03-29 10:55:24] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:55:24] Schedules count: 16
Schedule IDs: 40, 39, 38, 37, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23
Schedule Names: Test Birthday Email Names, Test Birthday Email Names, Test Birthday Email Names, Test Birthday Email Names, ddddd, ffffff, tttt, Test Birthday Email Fix, Test Birthday Email Fix, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, , active, , , , , , , , active, , active

[2025-03-29 10:56:10] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:56:10] Schedules count: 16
Schedule IDs: 40, 39, 38, 37, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23
Schedule Names: Test Birthday Email Names, Test Birthday Email Names, Test Birthday Email Names, Test Birthday Email Names, ddddd, ffffff, tttt, Test Birthday Email Fix, Test Birthday Email Fix, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, , active, , , , , , , , active, , active

[2025-03-29 10:56:34] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:56:34] Schedules count: 16
Schedule IDs: 40, 39, 38, 37, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23
Schedule Names: Test Birthday Email Names, Test Birthday Email Names, Test Birthday Email Names, Test Birthday Email Names, ddddd, ffffff, tttt, Test Birthday Email Fix, Test Birthday Email Fix, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, , active, , , , , , , , active, , active

[2025-03-29 10:56:46] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:56:46] Schedules count: 16
Schedule IDs: 40, 39, 38, 37, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23
Schedule Names: Test Birthday Email Names, Test Birthday Email Names, Test Birthday Email Names, Test Birthday Email Names, ddddd, ffffff, tttt, Test Birthday Email Fix, Test Birthday Email Fix, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, active, active, , , , , , , , active, , active

[2025-03-29 10:57:05] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:57:05] Schedules count: 16
Schedule IDs: 40, 39, 38, 37, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23
Schedule Names: Test Birthday Email Names, Test Birthday Email Names, Test Birthday Email Names, Test Birthday Email Names, ddddd, ffffff, tttt, Test Birthday Email Fix, Test Birthday Email Fix, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, active, active, , , , , , , , active, , active

[2025-03-29 10:58:03] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:58:03] Schedules count: 16
Schedule IDs: 40, 39, 38, 37, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23
Schedule Names: Test Birthday Email Names, Test Birthday Email Names, Test Birthday Email Names, Test Birthday Email Names, ddddd, ffffff, tttt, Test Birthday Email Fix, Test Birthday Email Fix, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, active, active, , , , , , , , active, , active

[2025-03-29 10:58:18] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:58:18] Schedules count: 15
Schedule IDs: 40, 39, 38, 37, 34, 33, 31, 30, 29, 28, 27, 26, 25, 24, 23
Schedule Names: Test Birthday Email Names, Test Birthday Email Names, Test Birthday Email Names, Test Birthday Email Names, ddddd, ffffff, Test Birthday Email Fix, Test Birthday Email Fix, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, active, active, , , , , , , active, , active

[2025-03-29 10:58:57] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 10:58:57] Schedules count: 15
Schedule IDs: 40, 39, 38, 37, 34, 33, 31, 30, 29, 28, 27, 26, 25, 24, 23
Schedule Names: Test Birthday Email Names, Test Birthday Email Names, Test Birthday Email Names, Test Birthday Email Names, ddddd, ffffff, Test Birthday Email Fix, Test Birthday Email Fix, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, active, active, , , , active, , , active, , active

[2025-03-29 11:12:38] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:12:38] Schedules count: 16
Schedule IDs: 41, 40, 39, 38, 37, 34, 33, 31, 30, 29, 28, 27, 26, 25, 24, 23
Schedule Names: Test Birthday Name Fix, Test Birthday Email Names, Test Birthday Email Names, Test Birthday Email Names, Test Birthday Email Names, ddddd, ffffff, Test Birthday Email Fix, Test Birthday Email Fix, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, active, , , , , , , , , active, , active

[2025-03-29 11:12:47] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:12:47] Schedules count: 15
Schedule IDs: 40, 39, 38, 37, 34, 33, 31, 30, 29, 28, 27, 26, 25, 24, 23
Schedule Names: Test Birthday Email Names, Test Birthday Email Names, Test Birthday Email Names, Test Birthday Email Names, ddddd, ffffff, Test Birthday Email Fix, Test Birthday Email Fix, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, , , , , , , , , active, , active

[2025-03-29 11:12:53] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:12:53] Schedules count: 14
Schedule IDs: 39, 38, 37, 34, 33, 31, 30, 29, 28, 27, 26, 25, 24, 23
Schedule Names: Test Birthday Email Names, Test Birthday Email Names, Test Birthday Email Names, ddddd, ffffff, Test Birthday Email Fix, Test Birthday Email Fix, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, , , , , , , , , active, , active

[2025-03-29 11:12:58] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:12:58] Schedules count: 13
Schedule IDs: 38, 37, 34, 33, 31, 30, 29, 28, 27, 26, 25, 24, 23
Schedule Names: Test Birthday Email Names, Test Birthday Email Names, ddddd, ffffff, Test Birthday Email Fix, Test Birthday Email Fix, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, , , , , , , , , active, , active

[2025-03-29 11:13:03] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:13:03] Schedules count: 12
Schedule IDs: 37, 34, 33, 31, 30, 29, 28, 27, 26, 25, 24, 23
Schedule Names: Test Birthday Email Names, ddddd, ffffff, Test Birthday Email Fix, Test Birthday Email Fix, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, , , , , , , , , active, , active

[2025-03-29 11:13:14] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:13:14] Schedules count: 11
Schedule IDs: 34, 33, 31, 30, 29, 28, 27, 26, 25, 24, 23
Schedule Names: ddddd, ffffff, Test Birthday Email Fix, Test Birthday Email Fix, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: , , , , , , , , active, , active

[2025-03-29 11:13:57] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:13:57] Schedules count: 10
Schedule IDs: 34, 33, 31, 29, 28, 27, 26, 25, 24, 23
Schedule Names: ddddd, ffffff, Test Birthday Email Fix, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: , , , , , , , active, , active

[2025-03-29 11:14:04] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:14:04] Schedules count: 9
Schedule IDs: 34, 33, 31, 28, 27, 26, 25, 24, 23
Schedule Names: ddddd, ffffff, Test Birthday Email Fix, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: , , , , , , active, , active

[2025-03-29 11:14:16] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:14:16] Schedules count: 8
Schedule IDs: 34, 33, 28, 27, 26, 25, 24, 23
Schedule Names: ddddd, ffffff, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: , , , , , active, , active

[2025-03-29 11:14:27] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:14:27] Schedules count: 8
Schedule IDs: 34, 33, 28, 27, 26, 25, 24, 23
Schedule Names: ddddd, ffffff, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: , , , , , active, , active

[2025-03-29 11:15:21] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:15:21] Schedules count: 8
Schedule IDs: 34, 33, 28, 27, 26, 25, 24, 23
Schedule Names: ddddd, ffffff, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: , , active, , , active, , active

[2025-03-29 11:22:21] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:22:21] Schedules count: 8
Schedule IDs: 34, 33, 28, 27, 26, 25, 24, 23
Schedule Names: ddddd, ffffff, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: , , active, , , active, , active

[2025-03-29 11:23:13] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:23:14] Schedules count: 8
Schedule IDs: 34, 33, 28, 27, 26, 25, 24, 23
Schedule Names: ddddd, ffffff, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: , , active, , , active, , active

[2025-03-29 11:24:08] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:24:08] Schedules count: 8
Schedule IDs: 34, 33, 28, 27, 26, 25, 24, 23
Schedule Names: ddddd, ffffff, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: , , active, , , active, , active

[2025-03-29 11:24:23] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:24:23] Schedules count: 8
Schedule IDs: 34, 33, 28, 27, 26, 25, 24, 23
Schedule Names: ddddd, ffffff, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, , active, , , active, , active

[2025-03-29 11:24:29] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:24:29] Schedules count: 8
Schedule IDs: 34, 33, 28, 27, 26, 25, 24, 23
Schedule Names: ddddd, ffffff, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, , , active, , active

[2025-03-29 11:25:33] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:25:33] Schedules count: 8
Schedule IDs: 34, 33, 28, 27, 26, 25, 24, 23
Schedule Names: ddddd, ffffff, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, , , active, , active

[2025-03-29 11:27:41] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:27:41] Schedules count: 8
Schedule IDs: 34, 33, 28, 27, 26, 25, 24, 23
Schedule Names: ddddd, ffffff, rhondadddd, Godwin Bointadddd, Test Placeholder Fix, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, , , active, , active

[2025-03-29 11:27:50] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:27:50] Schedules count: 7
Schedule IDs: 34, 33, 28, 27, 25, 24, 23
Schedule Names: ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, , active, , active

[2025-03-29 11:28:32] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:28:32] Schedules count: 7
Schedule IDs: 34, 33, 28, 27, 25, 24, 23
Schedule Names: ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, , active, , active

[2025-03-29 11:29:10] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:29:10] Schedules count: 7
Schedule IDs: 34, 33, 28, 27, 25, 24, 23
Schedule Names: ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, , active, , active

[2025-03-29 11:29:37] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:29:37] Schedules count: 7
Schedule IDs: 34, 33, 28, 27, 25, 24, 23
Schedule Names: ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, , active, , active

[2025-03-29 11:32:09] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:32:09] Schedules count: 8
Schedule IDs: 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, , active, , active

[2025-03-29 11:33:42] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:33:42] Schedules count: 8
Schedule IDs: 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, , active, , active

[2025-03-29 11:34:08] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:34:08] Schedules count: 8
Schedule IDs: 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, , active, , active

[2025-03-29 11:34:51] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:34:51] Schedules count: 8
Schedule IDs: 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, , active, , active

[2025-03-29 11:36:57] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:36:57] Schedules count: 8
Schedule IDs: 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, , active, , active

[2025-03-29 11:37:33] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:37:33] Schedules count: 9
Schedule IDs: 43, 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: rhondassssss, rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, active, , active, , active

[2025-03-29 11:38:09] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:38:09] Schedules count: 9
Schedule IDs: 43, 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: rhondassssss, rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, active, , active, , active

[2025-03-29 11:38:57] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:38:57] Schedules count: 9
Schedule IDs: 43, 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: rhondassssss, rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, active, , active, , active

[2025-03-29 11:41:43] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:41:43] Schedules count: 9
Schedule IDs: 43, 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: rhondassssss, rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, active, , active, , active

[2025-03-29 11:44:11] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:44:11] Schedules count: 9
Schedule IDs: 43, 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: rhondassssss, rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, active, , active, , active

[2025-03-29 11:44:43] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:44:43] Schedules count: 10
Schedule IDs: 44, 43, 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: rhondarrttt, rhondassssss, rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, active, active, , active, , active

[2025-03-29 11:45:13] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:45:13] Schedules count: 11
Schedule IDs: 45, 44, 43, 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: fgfgfggf, rhondarrttt, rhondassssss, rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, active, active, active, , active, , active

[2025-03-29 11:46:15] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:46:15] Schedules count: 11
Schedule IDs: 45, 44, 43, 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: fgfgfggf, rhondarrttt, rhondassssss, rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, active, active, active, active, , active, , active

[2025-03-29 11:50:57] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:50:57] Schedules count: 12
Schedule IDs: 46, 45, 44, 43, 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: Mike Johnneeee, fgfgfggf, rhondarrttt, rhondassssss, rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, , , , , , , active, , active

[2025-03-29 11:51:33] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:51:33] Schedules count: 12
Schedule IDs: 46, 45, 44, 43, 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: Mike Johnneeee, fgfgfggf, rhondarrttt, rhondassssss, rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, active, active, , , , , , , active, , active

[2025-03-29 11:58:44] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:58:44] Schedules count: 12
Schedule IDs: 46, 45, 44, 43, 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: Mike Johnneeee, fgfgfggf, rhondarrttt, rhondassssss, rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, , active, , , , , , , active, , active

[2025-03-29 11:59:26] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 11:59:26] Schedules count: 12
Schedule IDs: 46, 45, 44, 43, 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: Mike Johnneeee, fgfgfggf, rhondarrttt, rhondassssss, rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, , active, , , , , , , active, , active

[2025-03-29 12:02:32] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 12:02:32] Schedules count: 12
Schedule IDs: 46, 45, 44, 43, 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: Mike Johnneeee, fgfgfggf, rhondarrttt, rhondassssss, rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: , , active, , , , , , , active, , active

[2025-03-29 12:02:35] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 12:02:35] Schedules count: 12
Schedule IDs: 46, 45, 44, 43, 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: Mike Johnneeee, fgfgfggf, rhondarrttt, rhondassssss, rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: , , active, , , , , , , active, , active

[2025-03-29 12:03:07] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 12:03:07] Schedules count: 13
Schedule IDs: 47, 46, 45, 44, 43, 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: Godwin Bointa3333, Mike Johnneeee, fgfgfggf, rhondarrttt, rhondassssss, rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, , , active, , , , , , , active, , active

[2025-03-29 12:03:48] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 12:03:48] Schedules count: 13
Schedule IDs: 47, 46, 45, 44, 43, 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: Godwin Bointa3333, Mike Johnneeee, fgfgfggf, rhondarrttt, rhondassssss, rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, , , active, , , , , , , active, , active

[2025-03-29 12:06:12] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 12:06:12] Schedules count: 13
Schedule IDs: 47, 46, 45, 44, 43, 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: Godwin Bointa3333, Mike Johnneeee, fgfgfggf, rhondarrttt, rhondassssss, rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, , active, active, , , , , , , active, , active

[2025-03-29 12:19:11] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 12:19:11] Schedules count: 13
Schedule IDs: 47, 46, 45, 44, 43, 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: Godwin Bointa3333, Mike Johnneeee, fgfgfggf, rhondarrttt, rhondassssss, rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, , , active, , , , , , , active, , active

[2025-03-29 12:20:56] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 12:20:56] Schedules count: 13
Schedule IDs: 47, 46, 45, 44, 43, 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: Godwin Bointa3333, Mike Johnneeee, fgfgfggf, rhondarrttt, rhondassssss, rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, , , active, , , , , , , active, , active

[2025-03-29 12:25:51] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 12:25:51] Schedules count: 12
Schedule IDs: 46, 45, 44, 43, 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: Mike Johnneeee, fgfgfggf, rhondarrttt, rhondassssss, rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: , , active, , , , , , , active, , active

[2025-03-29 12:25:55] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 12:25:55] Schedules count: 11
Schedule IDs: 45, 44, 43, 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: fgfgfggf, rhondarrttt, rhondassssss, rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: , active, , , , , , , active, , active

[2025-03-29 12:26:05] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 12:26:05] Schedules count: 10
Schedule IDs: 45, 44, 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: fgfgfggf, rhondarrttt, rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: , active, , , , , , active, , active

[2025-03-29 12:26:19] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 12:26:19] Schedules count: 9
Schedule IDs: 45, 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: fgfgfggf, rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: , , , , , , active, , active

[2025-03-29 12:45:36] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 12:45:36] Schedules count: 9
Schedule IDs: 45, 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: fgfgfggf, rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: , , , , , , active, , active

[2025-03-29 12:46:14] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 12:46:14] Schedules count: 9
Schedule IDs: 45, 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: fgfgfggf, rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, , , , , , active, , active

[2025-03-29 12:46:49] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 12:46:49] Schedules count: 9
Schedule IDs: 45, 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: fgfgfggf, rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: active, , , , , , active, , active

[2025-03-29 12:47:02] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-29 12:47:02] Schedules count: 9
Schedule IDs: 45, 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: fgfgfggf, rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: , , , , , , active, , active

[2025-03-30 05:10:05] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-30 05:10:05] Schedules count: 9
Schedule IDs: 45, 42, 34, 33, 28, 27, 25, 24, 23
Schedule Names: fgfgfggf, rhondarrrrrcc, ddddd, ffffff, rhondadddd, Godwin Bointadddd, rhondadd, Ndibbbnnnn, Ndibbb
Schedule Statuses: , , , , , , active, , active

[2025-03-30 05:57:08] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-30 05:57:08] Schedules count: 0
Schedule IDs: 
Schedule Names: 
Schedule Statuses: 

[2025-03-31 09:42:14] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-03-31 09:42:14] Schedules count: 0
Schedule IDs: 
Schedule Names: 
Schedule Statuses: 

[2025-04-05 06:30:45] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-04-05 06:30:45] Schedules count: 0
Schedule IDs: 
Schedule Names: 
Schedule Statuses: 

[2025-06-27 14:02:20] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-06-27 14:02:28] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-06-27 14:03:24] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-06-27 14:14:36] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-06-27 14:14:36] Schedules count: 0
Schedule IDs: 
Schedule Names: 
Schedule Statuses: 

[2025-06-27 14:20:18] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-06-27 14:20:18] Schedules count: 0
Schedule IDs: 
Schedule Names: 
Schedule Statuses: 

[2025-06-27 14:24:14] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-06-27 14:24:14] Schedules count: 0
Schedule IDs: 
Schedule Names: 
Schedule Statuses: 

[2025-06-27 14:24:40] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-06-27 14:24:40] Schedules count: 0
Schedule IDs: 
Schedule Names: 
Schedule Statuses: 

[2025-06-28 12:01:53] SQL Query: 
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC

[2025-06-28 12:01:54] Schedules count: 0
Schedule IDs: 
Schedule Names: 
Schedule Statuses: 

