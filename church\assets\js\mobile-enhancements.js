/**
 * Mobile Enhancements JavaScript
 * Comprehensive mobile experience improvements
 * Version: 1.0.0
 */

(function() {
    'use strict';

    // Mobile detection and setup
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    
    // Initialize mobile enhancements when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initMobileEnhancements);
    } else {
        initMobileEnhancements();
    }

    function initMobileEnhancements() {
        setupViewportMeta();
        setupTouchEnhancements();
        setupMobileNavigation();
        setupMobileModals();
        setupMobileForms();
        setupMobileTables();
        setupSwipeGestures();
        setupMobileOptimizations();
        setupAccessibility();
    }

    // Ensure proper viewport meta tag
    function setupViewportMeta() {
        let viewport = document.querySelector('meta[name="viewport"]');
        if (!viewport) {
            viewport = document.createElement('meta');
            viewport.name = 'viewport';
            document.head.appendChild(viewport);
        }
        viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes';
    }

    // Touch enhancements
    function setupTouchEnhancements() {
        if (!isTouch) return;

        // Add touch class to body
        document.body.classList.add('touch-device');

        // Improve button touch feedback
        const buttons = document.querySelectorAll('.btn, button, [role="button"]');
        buttons.forEach(button => {
            button.addEventListener('touchstart', function() {
                this.classList.add('btn-pressed');
            });
            
            button.addEventListener('touchend', function() {
                setTimeout(() => {
                    this.classList.remove('btn-pressed');
                }, 150);
            });
        });

        // Prevent double-tap zoom on buttons
        buttons.forEach(button => {
            button.addEventListener('touchend', function(e) {
                e.preventDefault();
                this.click();
            });
        });
    }

    // Mobile navigation enhancements
    function setupMobileNavigation() {
        const navbar = document.querySelector('.navbar');
        const navbarToggler = document.querySelector('.navbar-toggler');
        const navbarCollapse = document.querySelector('.navbar-collapse');

        if (navbarToggler && navbarCollapse) {
            // Close mobile menu when clicking outside
            document.addEventListener('click', function(e) {
                if (window.innerWidth <= 768 && 
                    navbarCollapse.classList.contains('show') &&
                    !navbar.contains(e.target)) {
                    navbarToggler.click();
                }
            });

            // Close mobile menu when clicking on nav links
            const navLinks = navbarCollapse.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', function() {
                    if (window.innerWidth <= 768 && navbarCollapse.classList.contains('show')) {
                        setTimeout(() => navbarToggler.click(), 100);
                    }
                });
            });
        }

        // Add mobile menu indicator
        if (navbarToggler) {
            navbarToggler.innerHTML = `
                <span class="navbar-toggler-icon"></span>
                <span class="visually-hidden">Toggle navigation</span>
            `;
        }
    }

    // Mobile modal enhancements
    function setupMobileModals() {
        const modals = document.querySelectorAll('.modal');
        
        modals.forEach(modal => {
            // Prevent body scroll when modal is open on mobile
            modal.addEventListener('shown.bs.modal', function() {
                if (isMobile) {
                    document.body.style.overflow = 'hidden';
                    document.body.style.position = 'fixed';
                    document.body.style.width = '100%';
                }
            });

            modal.addEventListener('hidden.bs.modal', function() {
                if (isMobile) {
                    document.body.style.overflow = '';
                    document.body.style.position = '';
                    document.body.style.width = '';
                }
            });

            // Add swipe down to close on mobile
            if (isTouch) {
                let startY = 0;
                const modalDialog = modal.querySelector('.modal-dialog');
                
                modalDialog.addEventListener('touchstart', function(e) {
                    startY = e.touches[0].clientY;
                });

                modalDialog.addEventListener('touchmove', function(e) {
                    const currentY = e.touches[0].clientY;
                    const diff = currentY - startY;
                    
                    if (diff > 100) {
                        const modalInstance = bootstrap.Modal.getInstance(modal);
                        if (modalInstance) {
                            modalInstance.hide();
                        }
                    }
                });
            }
        });
    }

    // Mobile form enhancements
    function setupMobileForms() {
        const forms = document.querySelectorAll('form');
        
        forms.forEach(form => {
            // Auto-scroll to form errors on mobile
            const submitButton = form.querySelector('[type="submit"]');
            if (submitButton) {
                submitButton.addEventListener('click', function() {
                    setTimeout(() => {
                        const firstError = form.querySelector('.is-invalid, .error');
                        if (firstError && isMobile) {
                            firstError.scrollIntoView({ 
                                behavior: 'smooth', 
                                block: 'center' 
                            });
                        }
                    }, 100);
                });
            }
        });

        // Improve input focus on mobile
        const inputs = document.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                if (isMobile) {
                    // Scroll input into view with some padding
                    setTimeout(() => {
                        this.scrollIntoView({ 
                            behavior: 'smooth', 
                            block: 'center' 
                        });
                    }, 300);
                }
            });
        });
    }

    // Mobile table enhancements
    function setupMobileTables() {
        const tables = document.querySelectorAll('.table');
        
        tables.forEach(table => {
            // Add horizontal scroll indicator
            const wrapper = table.closest('.table-responsive');
            if (wrapper) {
                wrapper.addEventListener('scroll', function() {
                    const scrollLeft = this.scrollLeft;
                    const scrollWidth = this.scrollWidth;
                    const clientWidth = this.clientWidth;
                    
                    // Add visual indicators for scrollable content
                    if (scrollLeft > 0) {
                        this.classList.add('scrolled-left');
                    } else {
                        this.classList.remove('scrolled-left');
                    }
                    
                    if (scrollLeft < scrollWidth - clientWidth - 1) {
                        this.classList.add('scrollable-right');
                    } else {
                        this.classList.remove('scrollable-right');
                    }
                });
            }
        });
    }

    // Swipe gesture support
    function setupSwipeGestures() {
        if (!isTouch) return;

        let startX = 0;
        let startY = 0;
        
        document.addEventListener('touchstart', function(e) {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        });

        document.addEventListener('touchend', function(e) {
            if (!startX || !startY) return;
            
            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;
            
            const diffX = startX - endX;
            const diffY = startY - endY;
            
            // Horizontal swipe
            if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
                if (diffX > 0) {
                    // Swipe left
                    document.dispatchEvent(new CustomEvent('swipeLeft'));
                } else {
                    // Swipe right
                    document.dispatchEvent(new CustomEvent('swipeRight'));
                }
            }
            
            // Vertical swipe
            if (Math.abs(diffY) > Math.abs(diffX) && Math.abs(diffY) > 50) {
                if (diffY > 0) {
                    // Swipe up
                    document.dispatchEvent(new CustomEvent('swipeUp'));
                } else {
                    // Swipe down
                    document.dispatchEvent(new CustomEvent('swipeDown'));
                }
            }
            
            startX = 0;
            startY = 0;
        });
    }

    // Mobile-specific optimizations
    function setupMobileOptimizations() {
        // Lazy load images on mobile
        if ('IntersectionObserver' in window) {
            const images = document.querySelectorAll('img[data-src]');
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.removeAttribute('data-src');
                        imageObserver.unobserve(img);
                    }
                });
            });
            
            images.forEach(img => imageObserver.observe(img));
        }

        // Optimize animations for mobile
        if (isMobile) {
            const style = document.createElement('style');
            style.textContent = `
                * {
                    animation-duration: 0.3s !important;
                    transition-duration: 0.3s !important;
                }
            `;
            document.head.appendChild(style);
        }

        // Add mobile-specific classes
        document.body.classList.add(isMobile ? 'mobile-device' : 'desktop-device');
        document.body.classList.add(isTouch ? 'touch-device' : 'no-touch');
    }

    // Accessibility improvements for mobile
    function setupAccessibility() {
        // Improve focus management on mobile
        const focusableElements = document.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );

        focusableElements.forEach(element => {
            element.addEventListener('focus', function() {
                this.classList.add('focused');
            });
            
            element.addEventListener('blur', function() {
                this.classList.remove('focused');
            });
        });

        // Add skip link for mobile users
        const skipLink = document.createElement('a');
        skipLink.href = '#main-content';
        skipLink.textContent = 'Skip to main content';
        skipLink.className = 'skip-link visually-hidden-focusable';
        skipLink.style.cssText = `
            position: absolute;
            top: -40px;
            left: 6px;
            z-index: 1000;
            padding: 8px 16px;
            background: #000;
            color: #fff;
            text-decoration: none;
            border-radius: 4px;
        `;
        
        skipLink.addEventListener('focus', function() {
            this.style.top = '6px';
        });
        
        skipLink.addEventListener('blur', function() {
            this.style.top = '-40px';
        });
        
        document.body.insertBefore(skipLink, document.body.firstChild);
    }

    // Expose mobile utilities globally
    window.MobileEnhancements = {
        isMobile: isMobile,
        isTouch: isTouch,
        
        // Utility functions
        scrollToElement: function(element, offset = 0) {
            if (element) {
                const elementPosition = element.offsetTop - offset;
                window.scrollTo({
                    top: elementPosition,
                    behavior: 'smooth'
                });
            }
        },
        
        showMobileToast: function(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type} mobile-toast`;
            toast.textContent = message;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                z-index: 1050;
                min-width: 250px;
                text-align: center;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            `;
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }
    };

})();
