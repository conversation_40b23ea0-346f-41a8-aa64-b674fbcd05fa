<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

require_once '../config.php';
require_once '../includes/sms_functions.php';

// Get date range from request
$start_date = $_GET['start_date'] ?? date('Y-m-01'); // First day of current month
$end_date = $_GET['end_date'] ?? date('Y-m-d'); // Today

// Get SMS analytics
$sms_manager = getSMSManager();
$analytics = $sms_manager->getSMSAnalytics($start_date, $end_date);

// Get detailed SMS logs
$stmt = $pdo->prepare("
    SELECT sl.*, m.full_name, st.template_name
    FROM sms_logs sl
    LEFT JOIN members m ON sl.member_id = m.id
    LEFT JOIN sms_templates st ON sl.template_id = st.id
    WHERE DATE(sl.sent_at) BETWEEN ? AND ?
    ORDER BY sl.sent_at DESC
    LIMIT 100
");
$stmt->execute([$start_date, $end_date]);
$recent_sms = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get campaign performance
$stmt = $pdo->prepare("
    SELECT 
        campaign_name,
        total_recipients,
        sent_count,
        failed_count,
        CASE 
            WHEN total_recipients > 0 THEN ROUND((sent_count / total_recipients) * 100, 1)
            ELSE 0 
        END as success_rate,
        status,
        created_at
    FROM sms_campaigns
    WHERE DATE(created_at) BETWEEN ? AND ?
    ORDER BY created_at DESC
    LIMIT 10
");
$stmt->execute([$start_date, $end_date]);
$campaign_performance = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get hourly distribution
$stmt = $pdo->prepare("
    SELECT 
        HOUR(sent_at) as hour,
        COUNT(*) as count,
        SUM(CASE WHEN status = 'sent' THEN 1 ELSE 0 END) as sent_count
    FROM sms_logs
    WHERE DATE(sent_at) BETWEEN ? AND ?
    GROUP BY HOUR(sent_at)
    ORDER BY hour
");
$stmt->execute([$start_date, $end_date]);
$hourly_distribution = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get top performing templates
$stmt = $pdo->prepare("
    SELECT 
        st.template_name,
        st.template_type,
        COUNT(sl.id) as usage_count,
        SUM(CASE WHEN sl.status = 'sent' THEN 1 ELSE 0 END) as success_count,
        CASE 
            WHEN COUNT(sl.id) > 0 THEN ROUND((SUM(CASE WHEN sl.status = 'sent' THEN 1 ELSE 0 END) / COUNT(sl.id)) * 100, 1)
            ELSE 0 
        END as success_rate
    FROM sms_templates st
    LEFT JOIN sms_logs sl ON st.id = sl.template_id AND DATE(sl.sent_at) BETWEEN ? AND ?
    WHERE st.is_active = 1
    GROUP BY st.id, st.template_name, st.template_type
    HAVING usage_count > 0
    ORDER BY success_rate DESC, usage_count DESC
    LIMIT 10
");
$stmt->execute([$start_date, $end_date]);
$template_performance = $stmt->fetchAll(PDO::FETCH_ASSOC);

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-chart-bar me-2"></i>SMS Analytics</h2>
                <div class="d-flex gap-2">
                    <form method="get" class="d-flex gap-2">
                        <input type="date" name="start_date" value="<?php echo $start_date; ?>" class="form-control">
                        <input type="date" name="end_date" value="<?php echo $end_date; ?>" class="form-control">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter me-1"></i>Filter
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- Overview Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?php echo number_format($analytics['total'] ?? 0); ?></h4>
                                    <p class="mb-0">Total SMS Sent</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-sms fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?php echo number_format($analytics['sent'] ?? 0); ?></h4>
                                    <p class="mb-0">Successfully Delivered</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?php echo number_format($analytics['failed'] ?? 0); ?></h4>
                                    <p class="mb-0">Failed Deliveries</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-times-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?php echo $analytics['success_rate'] ?? 0; ?>%</h4>
                                    <p class="mb-0">Success Rate</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-percentage fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Charts Row -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>SMS by Type</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="smsTypeChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Hourly Distribution</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="hourlyChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Performance Tables -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-trophy me-2"></i>Top Performing Templates</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($template_performance)): ?>
                                <div class="text-center py-3">
                                    <i class="fas fa-file-alt fa-2x text-muted mb-2"></i>
                                    <p class="text-muted">No template usage data for this period</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Template</th>
                                                <th>Type</th>
                                                <th>Usage</th>
                                                <th>Success Rate</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($template_performance as $template): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($template['template_name']); ?></td>
                                                    <td>
                                                        <span class="badge bg-secondary">
                                                            <?php echo ucfirst($template['template_type']); ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo $template['usage_count']; ?></td>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="progress flex-grow-1 me-2" style="height: 15px;">
                                                                <div class="progress-bar bg-success" 
                                                                     style="width: <?php echo $template['success_rate']; ?>%"></div>
                                                            </div>
                                                            <small><?php echo $template['success_rate']; ?>%</small>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-bullhorn me-2"></i>Campaign Performance</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($campaign_performance)): ?>
                                <div class="text-center py-3">
                                    <i class="fas fa-bullhorn fa-2x text-muted mb-2"></i>
                                    <p class="text-muted">No campaigns in this period</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Campaign</th>
                                                <th>Recipients</th>
                                                <th>Success Rate</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($campaign_performance as $campaign): ?>
                                                <tr>
                                                    <td>
                                                        <?php echo htmlspecialchars($campaign['campaign_name']); ?>
                                                        <br>
                                                        <small class="text-muted">
                                                            <?php echo date('M j, g:i A', strtotime($campaign['created_at'])); ?>
                                                        </small>
                                                    </td>
                                                    <td>
                                                        <?php echo $campaign['total_recipients']; ?>
                                                        <br>
                                                        <small class="text-success"><?php echo $campaign['sent_count']; ?> sent</small>
                                                        <?php if ($campaign['failed_count'] > 0): ?>
                                                            <br><small class="text-danger"><?php echo $campaign['failed_count']; ?> failed</small>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="progress flex-grow-1 me-2" style="height: 15px;">
                                                                <div class="progress-bar bg-info" 
                                                                     style="width: <?php echo $campaign['success_rate']; ?>%"></div>
                                                            </div>
                                                            <small><?php echo $campaign['success_rate']; ?>%</small>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <?php
                                                        $status_class = [
                                                            'draft' => 'secondary',
                                                            'scheduled' => 'warning',
                                                            'sending' => 'primary',
                                                            'completed' => 'success',
                                                            'cancelled' => 'danger'
                                                        ];
                                                        ?>
                                                        <span class="badge bg-<?php echo $status_class[$campaign['status']] ?? 'secondary'; ?>">
                                                            <?php echo ucfirst($campaign['status']); ?>
                                                        </span>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Recent SMS Logs -->
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-history me-2"></i>Recent SMS Activity</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($recent_sms)): ?>
                                <div class="text-center py-3">
                                    <i class="fas fa-history fa-2x text-muted mb-2"></i>
                                    <p class="text-muted">No SMS activity in this period</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Recipient</th>
                                                <th>Phone Number</th>
                                                <th>Message</th>
                                                <th>Type</th>
                                                <th>Status</th>
                                                <th>Provider</th>
                                                <th>Sent At</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recent_sms as $sms): ?>
                                                <tr>
                                                    <td>
                                                        <?php if ($sms['full_name']): ?>
                                                            <?php echo htmlspecialchars($sms['full_name']); ?>
                                                        <?php else: ?>
                                                            <em class="text-muted">Unknown</em>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <code><?php echo htmlspecialchars($sms['phone_number']); ?></code>
                                                    </td>
                                                    <td>
                                                        <div style="max-width: 200px;">
                                                            <?php echo htmlspecialchars(substr($sms['message'], 0, 50)); ?>
                                                            <?php if (strlen($sms['message']) > 50): ?>...<?php endif; ?>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-info">
                                                            <?php echo ucfirst(str_replace('_', ' ', $sms['sms_type'])); ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <?php
                                                        $status_class = [
                                                            'sent' => 'success',
                                                            'failed' => 'danger',
                                                            'pending' => 'warning',
                                                            'delivered' => 'success',
                                                            'undelivered' => 'danger'
                                                        ];
                                                        ?>
                                                        <span class="badge bg-<?php echo $status_class[$sms['status']] ?? 'secondary'; ?>">
                                                            <?php echo ucfirst($sms['status']); ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <?php if ($sms['provider']): ?>
                                                            <span class="badge bg-secondary">
                                                                <?php echo ucfirst($sms['provider']); ?>
                                                            </span>
                                                        <?php else: ?>
                                                            <em class="text-muted">-</em>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php if ($sms['sent_at']): ?>
                                                            <?php echo date('M j, g:i A', strtotime($sms['sent_at'])); ?>
                                                        <?php else: ?>
                                                            <em class="text-muted">Not sent</em>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// SMS Type Chart
const smsTypeData = <?php echo json_encode($analytics['by_type'] ?? []); ?>;
const typeLabels = smsTypeData.map(item => item.sms_type.replace('_', ' ').toUpperCase());
const typeValues = smsTypeData.map(item => parseInt(item.count));

if (typeLabels.length > 0) {
    const smsTypeCtx = document.getElementById('smsTypeChart').getContext('2d');
    new Chart(smsTypeCtx, {
        type: 'doughnut',
        data: {
            labels: typeLabels,
            datasets: [{
                data: typeValues,
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF',
                    '#FF9F40'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
} else {
    document.getElementById('smsTypeChart').parentElement.innerHTML =
        '<div class="text-center py-4"><i class="fas fa-chart-pie fa-2x text-muted mb-2"></i><p class="text-muted">No data available</p></div>';
}

// Hourly Distribution Chart
const hourlyData = <?php echo json_encode($hourly_distribution); ?>;
const hourLabels = [];
const hourValues = [];

// Fill in all 24 hours
for (let i = 0; i < 24; i++) {
    hourLabels.push(i + ':00');
    const hourData = hourlyData.find(item => parseInt(item.hour) === i);
    hourValues.push(hourData ? parseInt(hourData.count) : 0);
}

const hourlyCtx = document.getElementById('hourlyChart').getContext('2d');
new Chart(hourlyCtx, {
    type: 'line',
    data: {
        labels: hourLabels,
        datasets: [{
            label: 'SMS Sent',
            data: hourValues,
            borderColor: '#36A2EB',
            backgroundColor: 'rgba(54, 162, 235, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});
</script>

<?php include 'includes/footer.php'; ?>
