<?php
/**
 * Enhanced SMS Functions for Church Campaign Application
 * Provides comprehensive SMS messaging capabilities with multiple providers
 */

require_once __DIR__ . '/../config.php';

class SMSManager {
    private $pdo;
    private $settings;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->loadSettings();
    }
    
    /**
     * Load SMS settings from database
     */
    private function loadSettings() {
        try {
            $stmt = $this->pdo->prepare("SELECT setting_key, setting_value FROM sms_settings");
            $stmt->execute();
            $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $this->settings = [];
            foreach ($settings as $setting) {
                $this->settings[$setting['setting_key']] = $setting['setting_value'];
            }
        } catch (PDOException $e) {
            error_log("Error loading SMS settings: " . $e->getMessage());
            $this->settings = [];
        }
    }
    
    /**
     * Send SMS using configured provider
     */
    public function sendSMS($to, $message, $type = 'general', $member_id = null, $template_id = null) {
        try {
            // Validate inputs
            if (empty($to) || empty($message)) {
                throw new Exception("Phone number and message are required");
            }
            
            // Format phone number
            $to = $this->formatPhoneNumber($to);
            
            // Check if SMS is enabled
            if (!$this->isSMSEnabled()) {
                throw new Exception("SMS notifications are disabled");
            }
            
            // Determine provider
            $provider = $this->getActiveProvider();
            if (!$provider) {
                throw new Exception("No SMS provider is configured");
            }
            
            // Send SMS based on provider
            $result = false;
            switch ($provider) {
                case 'twilio':
                    $result = $this->sendTwilioSMS($to, $message);
                    break;
                case 'nexmo':
                    $result = $this->sendNexmoSMS($to, $message);
                    break;
                default:
                    throw new Exception("Unsupported SMS provider: " . $provider);
            }
            
            // Log SMS
            $this->logSMS($to, $message, $type, $result ? 'sent' : 'failed', $member_id, $template_id, $provider);
            
            return $result;
            
        } catch (Exception $e) {
            error_log("SMS sending error: " . $e->getMessage());
            $this->logSMS($to, $message, $type, 'failed', $member_id, $template_id, null, $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send SMS using Twilio
     */
    private function sendTwilioSMS($to, $message) {
        $account_sid = $this->settings['twilio_account_sid'] ?? '';
        $auth_token = $this->settings['twilio_auth_token'] ?? '';
        $from_number = $this->settings['twilio_phone_number'] ?? '';
        
        if (empty($account_sid) || empty($auth_token) || empty($from_number)) {
            throw new Exception("Twilio credentials not configured");
        }
        
        $url = "https://api.twilio.com/2010-04-01/Accounts/{$account_sid}/Messages.json";
        
        $data = [
            'From' => $from_number,
            'To' => $to,
            'Body' => $message
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_USERPWD, $account_sid . ':' . $auth_token);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/x-www-form-urlencoded']);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($http_code >= 200 && $http_code < 300) {
            return true;
        } else {
            error_log("Twilio SMS failed: " . $response);
            return false;
        }
    }
    
    /**
     * Send SMS using Nexmo/Vonage
     */
    private function sendNexmoSMS($to, $message) {
        $api_key = $this->settings['nexmo_api_key'] ?? '';
        $api_secret = $this->settings['nexmo_api_secret'] ?? '';
        $from = $this->settings['nexmo_from'] ?? '';
        
        if (empty($api_key) || empty($api_secret) || empty($from)) {
            throw new Exception("Nexmo credentials not configured");
        }
        
        $url = "https://rest.nexmo.com/sms/json";
        
        $data = [
            'api_key' => $api_key,
            'api_secret' => $api_secret,
            'from' => $from,
            'to' => $to,
            'text' => $message
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/x-www-form-urlencoded']);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($http_code == 200) {
            $result = json_decode($response, true);
            if (isset($result['messages'][0]['status']) && $result['messages'][0]['status'] == '0') {
                return true;
            }
        }
        
        error_log("Nexmo SMS failed: " . $response);
        return false;
    }
    
    /**
     * Format phone number with country code
     */
    private function formatPhoneNumber($phone) {
        // Remove all non-numeric characters
        $phone = preg_replace('/[^0-9+]/', '', $phone);
        
        // If phone doesn't start with +, add default country code
        if (!str_starts_with($phone, '+')) {
            $default_code = $this->settings['default_country_code'] ?? '+1';
            $phone = $default_code . ltrim($phone, '0');
        }
        
        return $phone;
    }
    
    /**
     * Check if SMS is enabled
     */
    private function isSMSEnabled() {
        return ($this->settings['sms_notifications_enabled'] ?? '0') === '1';
    }
    
    /**
     * Get active SMS provider
     */
    private function getActiveProvider() {
        if (($this->settings['twilio_enabled'] ?? '0') === '1') {
            return 'twilio';
        }
        if (($this->settings['nexmo_enabled'] ?? '0') === '1') {
            return 'nexmo';
        }
        return null;
    }
    
    /**
     * Log SMS to database
     */
    private function logSMS($to, $message, $type, $status, $member_id = null, $template_id = null, $provider = null, $error = null) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO sms_logs (member_id, template_id, phone_number, message, sms_type, status, provider, error_message, sent_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            $stmt->execute([$member_id, $template_id, $to, $message, $type, $status, $provider, $error]);
        } catch (PDOException $e) {
            error_log("Error logging SMS: " . $e->getMessage());
        }
    }
    
    /**
     * Send birthday SMS
     */
    public function sendBirthdaySMS($member_id, $member_name, $phone_number, $template_id = null) {
        if (($this->settings['birthday_sms_enabled'] ?? '0') !== '1') {
            return false;
        }
        
        $template = $this->settings['birthday_sms_template'] ?? 'Happy Birthday {name}! - Freedom Assembly Church';
        $message = str_replace('{name}', $member_name, $template);
        
        return $this->sendSMS($phone_number, $message, 'birthday', $member_id, $template_id);
    }
    
    /**
     * Send event reminder SMS
     */
    public function sendEventReminderSMS($member_id, $member_name, $phone_number, $event_name, $event_date, $event_time, $event_location) {
        if (($this->settings['event_sms_enabled'] ?? '0') !== '1') {
            return false;
        }
        
        $template = $this->settings['event_sms_template'] ?? 'Reminder: {event_name} on {event_date} at {event_time}. {event_location}. - Freedom Assembly Church';
        $message = str_replace(
            ['{event_name}', '{event_date}', '{event_time}', '{event_location}'],
            [$event_name, $event_date, $event_time, $event_location],
            $template
        );
        
        return $this->sendSMS($phone_number, $message, 'event_reminder', $member_id);
    }
    
    /**
     * Send bulk SMS to multiple recipients
     */
    public function sendBulkSMS($recipients, $message, $type = 'bulk') {
        $results = [];
        foreach ($recipients as $recipient) {
            $member_id = $recipient['member_id'] ?? null;
            $phone = $recipient['phone_number'] ?? $recipient['phone'];
            $result = $this->sendSMS($phone, $message, $type, $member_id);
            $results[] = [
                'phone' => $phone,
                'member_id' => $member_id,
                'success' => $result
            ];
        }
        return $results;
    }
    
    /**
     * Get SMS analytics
     */
    public function getSMSAnalytics($start_date = null, $end_date = null) {
        try {
            $where_clause = "WHERE 1=1";
            $params = [];
            
            if ($start_date) {
                $where_clause .= " AND sent_at >= ?";
                $params[] = $start_date;
            }
            
            if ($end_date) {
                $where_clause .= " AND sent_at <= ?";
                $params[] = $end_date . ' 23:59:59';
            }
            
            // Total SMS count
            $stmt = $this->pdo->prepare("SELECT COUNT(*) as total FROM sms_logs $where_clause");
            $stmt->execute($params);
            $total = $stmt->fetchColumn();
            
            // Success rate
            $stmt = $this->pdo->prepare("SELECT COUNT(*) as sent FROM sms_logs $where_clause AND status = 'sent'");
            $stmt->execute($params);
            $sent = $stmt->fetchColumn();
            
            // By type
            $stmt = $this->pdo->prepare("SELECT sms_type, COUNT(*) as count FROM sms_logs $where_clause GROUP BY sms_type");
            $stmt->execute($params);
            $by_type = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // By provider
            $stmt = $this->pdo->prepare("SELECT provider, COUNT(*) as count FROM sms_logs $where_clause GROUP BY provider");
            $stmt->execute($params);
            $by_provider = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return [
                'total' => $total,
                'sent' => $sent,
                'failed' => $total - $sent,
                'success_rate' => $total > 0 ? round(($sent / $total) * 100, 2) : 0,
                'by_type' => $by_type,
                'by_provider' => $by_provider
            ];
            
        } catch (PDOException $e) {
            error_log("Error getting SMS analytics: " . $e->getMessage());
            return null;
        }
    }
}

/**
 * Helper function to get SMS manager instance
 */
function getSMSManager() {
    global $pdo;
    return new SMSManager($pdo);
}

/**
 * Quick function to send SMS
 */
function send_sms($to, $message, $type = 'general', $member_id = null) {
    $sms_manager = getSMSManager();
    return $sms_manager->sendSMS($to, $message, $type, $member_id);
}
