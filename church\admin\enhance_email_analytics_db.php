<?php
/**
 * Enhanced Email Analytics Database Schema Enhancement
 * 
 * This script enhances the email analytics database schema to support:
 * - Click tracking with detailed metrics
 * - Advanced engagement analytics
 * - Email performance metrics
 * - Recipient behavior tracking
 * - Campaign analytics
 */

require_once '../config.php';

// Check if user is logged in as admin
session_start();
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

echo "<h2>Enhanced Email Analytics Database Schema Enhancement</h2>";
echo "<p>Enhancing database schema for comprehensive email analytics...</p>";

try {
    // Start transaction
    $pdo->beginTransaction();
    
    echo "<h3>1. Enhancing email_tracking table with click tracking...</h3>";
    
    // Check if click tracking columns exist
    $stmt = $pdo->prepare("SHOW COLUMNS FROM email_tracking LIKE 'clicked_at'");
    $stmt->execute();
    $clickedAtExists = $stmt->fetch();
    
    if (!$clickedAtExists) {
        // Add click tracking columns to email_tracking table
        $alterQueries = [
            "ALTER TABLE email_tracking ADD COLUMN clicked_at TIMESTAMP NULL DEFAULT NULL AFTER opened_count",
            "ALTER TABLE email_tracking ADD COLUMN clicked_count INT DEFAULT 0 AFTER clicked_at",
            "ALTER TABLE email_tracking ADD COLUMN last_click_at TIMESTAMP NULL DEFAULT NULL AFTER clicked_count",
            "ALTER TABLE email_tracking ADD COLUMN bounce_type ENUM('soft', 'hard', 'none') DEFAULT 'none' AFTER last_click_at",
            "ALTER TABLE email_tracking ADD COLUMN bounced_at TIMESTAMP NULL DEFAULT NULL AFTER bounce_type",
            "ALTER TABLE email_tracking ADD COLUMN unsubscribed_at TIMESTAMP NULL DEFAULT NULL AFTER bounced_at",
            "ALTER TABLE email_tracking ADD COLUMN engagement_score DECIMAL(5,2) DEFAULT 0.00 AFTER unsubscribed_at",
            "ALTER TABLE email_tracking ADD COLUMN device_type VARCHAR(50) DEFAULT NULL AFTER engagement_score",
            "ALTER TABLE email_tracking ADD COLUMN location_country VARCHAR(100) DEFAULT NULL AFTER device_type",
            "ALTER TABLE email_tracking ADD COLUMN location_city VARCHAR(100) DEFAULT NULL AFTER location_country"
        ];
        
        foreach ($alterQueries as $query) {
            try {
                $pdo->exec($query);
                echo "<p>✓ " . htmlspecialchars($query) . "</p>";
            } catch (PDOException $e) {
                echo "<p>⚠ Warning: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        }
        
        // Add indexes for performance
        $indexQueries = [
            "CREATE INDEX idx_clicked_at ON email_tracking(clicked_at)",
            "CREATE INDEX idx_engagement_score ON email_tracking(engagement_score)",
            "CREATE INDEX idx_device_type ON email_tracking(device_type)"
        ];
        
        foreach ($indexQueries as $query) {
            try {
                $pdo->exec($query);
                echo "<p>✓ " . htmlspecialchars($query) . "</p>";
            } catch (PDOException $e) {
                echo "<p>⚠ Index warning: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        }
    } else {
        echo "<p>✓ Click tracking columns already exist in email_tracking table</p>";
    }
    
    echo "<h3>2. Creating email_analytics_summary table...</h3>";
    
    // Create email analytics summary table for performance
    $createSummaryTable = "
    CREATE TABLE IF NOT EXISTS email_analytics_summary (
        id INT AUTO_INCREMENT PRIMARY KEY,
        date_period DATE NOT NULL,
        period_type ENUM('daily', 'weekly', 'monthly') DEFAULT 'daily',
        total_sent INT DEFAULT 0,
        total_delivered INT DEFAULT 0,
        total_opened INT DEFAULT 0,
        total_clicked INT DEFAULT 0,
        total_bounced INT DEFAULT 0,
        total_unsubscribed INT DEFAULT 0,
        unique_opens INT DEFAULT 0,
        unique_clicks INT DEFAULT 0,
        open_rate DECIMAL(5,2) DEFAULT 0.00,
        click_rate DECIMAL(5,2) DEFAULT 0.00,
        click_to_open_rate DECIMAL(5,2) DEFAULT 0.00,
        bounce_rate DECIMAL(5,2) DEFAULT 0.00,
        unsubscribe_rate DECIMAL(5,2) DEFAULT 0.00,
        engagement_rate DECIMAL(5,2) DEFAULT 0.00,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_period (date_period, period_type),
        INDEX idx_date_period (date_period),
        INDEX idx_period_type (period_type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    $pdo->exec($createSummaryTable);
    echo "<p>✓ Created email_analytics_summary table</p>";
    
    echo "<h3>3. Creating email_click_tracking table...</h3>";
    
    // Create detailed click tracking table
    $createClickTable = "
    CREATE TABLE IF NOT EXISTS email_click_tracking (
        id INT AUTO_INCREMENT PRIMARY KEY,
        tracking_id VARCHAR(64) NOT NULL,
        member_id INT,
        email_type VARCHAR(50),
        clicked_url TEXT NOT NULL,
        clicked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        user_agent VARCHAR(500),
        ip_address VARCHAR(45),
        device_type VARCHAR(50),
        browser_name VARCHAR(100),
        operating_system VARCHAR(100),
        location_country VARCHAR(100),
        location_city VARCHAR(100),
        referrer TEXT,
        INDEX idx_tracking_id (tracking_id),
        INDEX idx_member_id (member_id),
        INDEX idx_clicked_at (clicked_at),
        INDEX idx_email_type (email_type),
        FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    $pdo->exec($createClickTable);
    echo "<p>✓ Created email_click_tracking table</p>";
    
    echo "<h3>4. Creating email_campaign_analytics table...</h3>";
    
    // Create campaign analytics table
    $createCampaignTable = "
    CREATE TABLE IF NOT EXISTS email_campaign_analytics (
        id INT AUTO_INCREMENT PRIMARY KEY,
        campaign_name VARCHAR(255) NOT NULL,
        campaign_type VARCHAR(100),
        template_id INT,
        start_date DATE,
        end_date DATE,
        total_recipients INT DEFAULT 0,
        emails_sent INT DEFAULT 0,
        emails_delivered INT DEFAULT 0,
        emails_opened INT DEFAULT 0,
        emails_clicked INT DEFAULT 0,
        emails_bounced INT DEFAULT 0,
        emails_unsubscribed INT DEFAULT 0,
        open_rate DECIMAL(5,2) DEFAULT 0.00,
        click_rate DECIMAL(5,2) DEFAULT 0.00,
        conversion_rate DECIMAL(5,2) DEFAULT 0.00,
        roi DECIMAL(10,2) DEFAULT 0.00,
        cost DECIMAL(10,2) DEFAULT 0.00,
        revenue DECIMAL(10,2) DEFAULT 0.00,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_campaign_name (campaign_name),
        INDEX idx_campaign_type (campaign_type),
        INDEX idx_start_date (start_date),
        FOREIGN KEY (template_id) REFERENCES email_templates(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    $pdo->exec($createCampaignTable);
    echo "<p>✓ Created email_campaign_analytics table</p>";
    
    echo "<h3>5. Creating email_recipient_segments table...</h3>";
    
    // Create recipient segmentation table
    $createSegmentTable = "
    CREATE TABLE IF NOT EXISTS email_recipient_segments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        segment_name VARCHAR(255) NOT NULL,
        segment_description TEXT,
        criteria JSON,
        member_count INT DEFAULT 0,
        engagement_score DECIMAL(5,2) DEFAULT 0.00,
        avg_open_rate DECIMAL(5,2) DEFAULT 0.00,
        avg_click_rate DECIMAL(5,2) DEFAULT 0.00,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_segment_name (segment_name),
        INDEX idx_engagement_score (engagement_score)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    $pdo->exec($createSegmentTable);
    echo "<p>✓ Created email_recipient_segments table</p>";
    
    // Commit transaction
    $pdo->commit();
    
    echo "<h3>✅ Database Enhancement Complete!</h3>";
    echo "<p>Enhanced email analytics database schema has been successfully implemented.</p>";
    echo "<p><strong>New Features Added:</strong></p>";
    echo "<ul>";
    echo "<li>Click tracking with detailed metrics</li>";
    echo "<li>Engagement scoring system</li>";
    echo "<li>Device and location tracking</li>";
    echo "<li>Campaign analytics</li>";
    echo "<li>Recipient segmentation</li>";
    echo "<li>Performance summary tables</li>";
    echo "<li>Bounce and unsubscribe tracking</li>";
    echo "</ul>";
    
    echo "<p><a href='email_analytics.php' class='btn btn-primary'>View Enhanced Analytics Dashboard</a></p>";
    
} catch (Exception $e) {
    // Rollback transaction on error
    $pdo->rollback();
    echo "<h3>❌ Error enhancing database schema:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
p { margin: 5px 0; }
.btn { padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
.btn:hover { background: #0056b3; }
</style>
