<?php
/**
 * Enhanced Email Tracking Endpoint
 * 
 * This enhanced tracking endpoint provides comprehensive email analytics including:
 * - Open tracking with device detection
 * - Click tracking with detailed metrics
 * - Engagement scoring
 * - Location detection
 * - Advanced analytics data collection
 */

require_once 'config.php';
require_once 'includes/enhanced_email_tracking.php';

// Get tracking parameters
$trackingId = $_GET['id'] ?? '';
$trackingType = $_GET['type'] ?? 'open';
$emailType = $_GET['email_type'] ?? 'general';
$memberId = isset($_GET['member_id']) ? intval($_GET['member_id']) : null;
$targetUrl = $_GET['url'] ?? '';

// Validate tracking ID
if (empty($trackingId)) {
    header('HTTP/1.1 400 Bad Request');
    exit('Missing tracking ID');
}

// Validate tracking type
if (!in_array($trackingType, ['open', 'click'])) {
    header('HTTP/1.1 400 Bad Request');
    exit('Invalid tracking type');
}

// Get user agent and IP address
$userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
$ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';

// Detect device type and location
$deviceType = detectDeviceType($userAgent);
$location = getLocationFromIP($ipAddress);

try {
    // Check if tracking record exists
    $stmt = $pdo->prepare("SELECT * FROM email_tracking WHERE tracking_id = ?");
    $stmt->execute([$trackingId]);
    $tracking = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$tracking) {
        // Create new tracking record if it doesn't exist
        $insertStmt = $pdo->prepare("
            INSERT INTO email_tracking 
            (tracking_id, member_id, email_type, sent_at, device_type, location_country, location_city, user_agent, ip_address) 
            VALUES (?, ?, ?, NOW(), ?, ?, ?, ?, ?)
        ");
        $insertStmt->execute([
            $trackingId, 
            $memberId, 
            $emailType, 
            $deviceType, 
            $location['country'], 
            $location['city'], 
            $userAgent, 
            $ipAddress
        ]);
        
        // Get the newly created record
        $stmt->execute([$trackingId]);
        $tracking = $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    if ($trackingType === 'open') {
        // Handle email open tracking
        $openCount = ($tracking['opened_count'] ?? 0) + 1;
        
        $updateStmt = $pdo->prepare("
            UPDATE email_tracking 
            SET 
                opened_at = CASE WHEN opened_at IS NULL THEN NOW() ELSE opened_at END,
                opened_count = ?,
                user_agent = ?,
                ip_address = ?,
                device_type = ?,
                location_country = ?,
                location_city = ?
            WHERE tracking_id = ?
        ");
        $updateStmt->execute([
            $openCount, 
            $userAgent, 
            $ipAddress, 
            $deviceType, 
            $location['country'], 
            $location['city'], 
            $trackingId
        ]);
        
        // Update engagement score
        if ($tracking['member_id']) {
            $engagementScore = calculateEngagementScore($tracking['member_id'], $emailType);
            $pdo->prepare("UPDATE email_tracking SET engagement_score = ? WHERE tracking_id = ?")
                ->execute([$engagementScore, $trackingId]);
        }
        
        // Return transparent 1x1 pixel
        header('Content-Type: image/gif');
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');
        echo base64_decode('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7');
        
    } elseif ($trackingType === 'click') {
        // Handle email click tracking
        $clickCount = ($tracking['clicked_count'] ?? 0) + 1;
        
        $updateStmt = $pdo->prepare("
            UPDATE email_tracking 
            SET 
                clicked_at = CASE WHEN clicked_at IS NULL THEN NOW() ELSE clicked_at END,
                clicked_count = ?,
                last_click_at = NOW(),
                user_agent = ?,
                ip_address = ?,
                device_type = ?,
                location_country = ?,
                location_city = ?
            WHERE tracking_id = ?
        ");
        $updateStmt->execute([
            $clickCount, 
            $userAgent, 
            $ipAddress, 
            $deviceType, 
            $location['country'], 
            $location['city'], 
            $trackingId
        ]);
        
        // Log detailed click tracking
        if (!empty($targetUrl)) {
            $clickStmt = $pdo->prepare("
                INSERT INTO email_click_tracking 
                (tracking_id, member_id, email_type, clicked_url, user_agent, ip_address, 
                 device_type, location_country, location_city) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $clickStmt->execute([
                $trackingId,
                $tracking['member_id'],
                $emailType,
                $targetUrl,
                $userAgent,
                $ipAddress,
                $deviceType,
                $location['country'],
                $location['city']
            ]);
        }
        
        // Update engagement score
        if ($tracking['member_id']) {
            $engagementScore = calculateEngagementScore($tracking['member_id'], $emailType);
            $pdo->prepare("UPDATE email_tracking SET engagement_score = ? WHERE tracking_id = ?")
                ->execute([$engagementScore, $trackingId]);
        }
        
        // Redirect to target URL if provided
        if (!empty($targetUrl)) {
            header('Location: ' . $targetUrl);
            exit;
        } else {
            // Return success response for AJAX calls
            header('Content-Type: application/json');
            echo json_encode(['status' => 'success', 'message' => 'Click tracked']);
        }
    }
    
} catch (Exception $e) {
    error_log('Enhanced email tracking error: ' . $e->getMessage());
    
    if ($trackingType === 'open') {
        // Still return the transparent pixel even if there's an error
        header('Content-Type: image/gif');
        echo base64_decode('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7');
    } else {
        // For click tracking, redirect to target URL if available
        if (!empty($targetUrl)) {
            header('Location: ' . $targetUrl);
        } else {
            header('Content-Type: application/json');
            echo json_encode(['status' => 'error', 'message' => 'Tracking failed']);
        }
    }
}
?>
