<?php
/**
 * Enhanced SMS System Database Setup
 * Creates necessary tables and enhances existing SMS infrastructure
 */

session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

require_once '../config.php';

$results = [];
$errors = [];

try {
    // Create SMS logs table
    $sql_sms_logs = "CREATE TABLE IF NOT EXISTS sms_logs (
        id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        member_id INT(11) NULL,
        template_id INT(11) NULL,
        phone_number VARCHAR(20) NOT NULL,
        message TEXT NOT NULL,
        sms_type ENUM('birthday', 'event_reminder', 'bulk', 'general', 'urgent') DEFAULT 'general',
        status ENUM('sent', 'failed', 'pending', 'delivered', 'undelivered') DEFAULT 'pending',
        provider VARCHAR(20) NULL,
        provider_message_id VARCHAR(100) NULL,
        error_message TEXT NULL,
        sent_at TIMESTAMP NULL,
        delivered_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_member_id (member_id),
        INDEX idx_status (status),
        INDEX idx_sms_type (sms_type),
        INDEX idx_sent_at (sent_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql_sms_logs);
    $results[] = "✅ SMS logs table created successfully";
    
    // Create SMS templates table
    $sql_sms_templates = "CREATE TABLE IF NOT EXISTS sms_templates (
        id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        template_name VARCHAR(100) NOT NULL,
        template_content TEXT NOT NULL,
        template_type ENUM('birthday', 'event_reminder', 'general', 'urgent') DEFAULT 'general',
        is_active TINYINT(1) DEFAULT 1,
        usage_count INT(11) DEFAULT 0,
        created_by INT(11) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_template_type (template_type),
        INDEX idx_is_active (is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql_sms_templates);
    $results[] = "✅ SMS templates table created successfully";
    
    // Create SMS campaigns table
    $sql_sms_campaigns = "CREATE TABLE IF NOT EXISTS sms_campaigns (
        id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        campaign_name VARCHAR(100) NOT NULL,
        template_id INT(11) NULL,
        message_content TEXT NOT NULL,
        target_audience ENUM('all_members', 'birthday_today', 'event_attendees', 'custom_group') DEFAULT 'all_members',
        target_criteria JSON NULL,
        total_recipients INT(11) DEFAULT 0,
        sent_count INT(11) DEFAULT 0,
        failed_count INT(11) DEFAULT 0,
        status ENUM('draft', 'scheduled', 'sending', 'completed', 'cancelled') DEFAULT 'draft',
        scheduled_at TIMESTAMP NULL,
        started_at TIMESTAMP NULL,
        completed_at TIMESTAMP NULL,
        created_by INT(11) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_status (status),
        INDEX idx_scheduled_at (scheduled_at),
        INDEX idx_created_by (created_by)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql_sms_campaigns);
    $results[] = "✅ SMS campaigns table created successfully";
    
    // Create SMS queue table for batch processing
    $sql_sms_queue = "CREATE TABLE IF NOT EXISTS sms_queue (
        id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        campaign_id INT(11) NULL,
        member_id INT(11) NULL,
        phone_number VARCHAR(20) NOT NULL,
        message TEXT NOT NULL,
        sms_type VARCHAR(20) DEFAULT 'general',
        priority TINYINT(1) DEFAULT 5,
        attempts INT(11) DEFAULT 0,
        max_attempts INT(11) DEFAULT 3,
        status ENUM('pending', 'processing', 'sent', 'failed', 'cancelled') DEFAULT 'pending',
        scheduled_at TIMESTAMP NULL,
        processed_at TIMESTAMP NULL,
        error_message TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_status (status),
        INDEX idx_priority (priority),
        INDEX idx_scheduled_at (scheduled_at),
        INDEX idx_campaign_id (campaign_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql_sms_queue);
    $results[] = "✅ SMS queue table created successfully";
    
    // Insert default SMS templates
    $default_templates = [
        [
            'template_name' => 'Birthday Greeting',
            'template_content' => 'Happy Birthday {name}! 🎂 May God bless you abundantly on your special day. Wishing you joy, peace, and happiness. - Freedom Assembly Church',
            'template_type' => 'birthday'
        ],
        [
            'template_name' => 'Event Reminder',
            'template_content' => 'Reminder: {event_name} is happening on {event_date} at {event_time}. Location: {event_location}. See you there! - Freedom Assembly Church',
            'template_type' => 'event_reminder'
        ],
        [
            'template_name' => 'Welcome Message',
            'template_content' => 'Welcome to Freedom Assembly Church, {name}! We\'re excited to have you as part of our church family. God bless you!',
            'template_type' => 'general'
        ],
        [
            'template_name' => 'Urgent Announcement',
            'template_content' => 'URGENT: {message} For more information, please contact the church office. - Freedom Assembly Church',
            'template_type' => 'urgent'
        ]
    ];
    
    foreach ($default_templates as $template) {
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO sms_templates (template_name, template_content, template_type, created_by)
            VALUES (?, ?, ?, ?)
        ");
        $stmt->execute([
            $template['template_name'],
            $template['template_content'],
            $template['template_type'],
            $_SESSION['admin_id']
        ]);
    }
    $results[] = "✅ Default SMS templates inserted successfully";
    
    // Add phone number validation to members table if not exists
    $stmt = $pdo->prepare("SHOW COLUMNS FROM members LIKE 'phone_verified'");
    $stmt->execute();
    if ($stmt->rowCount() == 0) {
        $pdo->exec("ALTER TABLE members ADD COLUMN phone_verified TINYINT(1) DEFAULT 0 AFTER phone_number");
        $results[] = "✅ Added phone_verified column to members table";
    }
    
    // Add SMS preferences to members table if not exists
    $stmt = $pdo->prepare("SHOW COLUMNS FROM members LIKE 'sms_notifications'");
    $stmt->execute();
    if ($stmt->rowCount() == 0) {
        $pdo->exec("ALTER TABLE members ADD COLUMN sms_notifications TINYINT(1) DEFAULT 1 AFTER phone_verified");
        $results[] = "✅ Added sms_notifications column to members table";
    }
    
    // Update existing SMS settings with new options
    $new_settings = [
        'sms_queue_enabled' => '1',
        'sms_batch_size' => '50',
        'sms_rate_limit' => '10',
        'sms_retry_attempts' => '3',
        'sms_analytics_enabled' => '1',
        'urgent_sms_enabled' => '1',
        'sms_webhook_url' => '',
        'sms_delivery_reports' => '1'
    ];
    
    foreach ($new_settings as $key => $value) {
        $stmt = $pdo->prepare("
            INSERT INTO sms_settings (setting_key, setting_value)
            VALUES (?, ?)
            ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
        ");
        $stmt->execute([$key, $value]);
    }
    $results[] = "✅ Enhanced SMS settings updated successfully";
    
    // Create SMS analytics summary table
    $sql_sms_analytics = "CREATE TABLE IF NOT EXISTS sms_analytics_summary (
        id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        date_period DATE NOT NULL,
        period_type ENUM('daily', 'weekly', 'monthly') DEFAULT 'daily',
        total_sent INT(11) DEFAULT 0,
        total_delivered INT(11) DEFAULT 0,
        total_failed INT(11) DEFAULT 0,
        birthday_sms INT(11) DEFAULT 0,
        event_sms INT(11) DEFAULT 0,
        bulk_sms INT(11) DEFAULT 0,
        urgent_sms INT(11) DEFAULT 0,
        delivery_rate DECIMAL(5,2) DEFAULT 0.00,
        cost_estimate DECIMAL(10,2) DEFAULT 0.00,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_period (date_period, period_type),
        INDEX idx_date_period (date_period),
        INDEX idx_period_type (period_type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql_sms_analytics);
    $results[] = "✅ SMS analytics summary table created successfully";
    
    // Test SMS system connectivity
    $sms_test_results = [];
    
    // Check Twilio configuration
    $stmt = $pdo->prepare("SELECT setting_value FROM sms_settings WHERE setting_key = 'twilio_enabled'");
    $stmt->execute();
    $twilio_enabled = $stmt->fetchColumn();
    
    if ($twilio_enabled === '1') {
        $sms_test_results[] = "✅ Twilio SMS provider is enabled";
    } else {
        $sms_test_results[] = "⚠️ Twilio SMS provider is disabled";
    }
    
    // Check Nexmo configuration
    $stmt = $pdo->prepare("SELECT setting_value FROM sms_settings WHERE setting_key = 'nexmo_enabled'");
    $stmt->execute();
    $nexmo_enabled = $stmt->fetchColumn();
    
    if ($nexmo_enabled === '1') {
        $sms_test_results[] = "✅ Nexmo SMS provider is enabled";
    } else {
        $sms_test_results[] = "⚠️ Nexmo SMS provider is disabled";
    }
    
    $results = array_merge($results, $sms_test_results);
    
} catch (PDOException $e) {
    $errors[] = "❌ Database error: " . $e->getMessage();
} catch (Exception $e) {
    $errors[] = "❌ Error: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced SMS System Setup - Freedom Assembly Church</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="fas fa-sms me-2"></i>Enhanced SMS System Setup</h4>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($results)): ?>
                            <div class="alert alert-success">
                                <h5><i class="fas fa-check-circle me-2"></i>Setup Results</h5>
                                <ul class="mb-0">
                                    <?php foreach ($results as $result): ?>
                                        <li><?php echo htmlspecialchars($result); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <h5><i class="fas fa-exclamation-triangle me-2"></i>Errors</h5>
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <h5><i class="fas fa-database me-2"></i>Database Tables Created</h5>
                                <ul class="list-group">
                                    <li class="list-group-item">📊 sms_logs - SMS delivery tracking</li>
                                    <li class="list-group-item">📝 sms_templates - Message templates</li>
                                    <li class="list-group-item">📢 sms_campaigns - Bulk messaging campaigns</li>
                                    <li class="list-group-item">⏰ sms_queue - Message queue processing</li>
                                    <li class="list-group-item">📈 sms_analytics_summary - Analytics data</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h5><i class="fas fa-cogs me-2"></i>Features Added</h5>
                                <ul class="list-group">
                                    <li class="list-group-item">🎂 Birthday SMS automation</li>
                                    <li class="list-group-item">📅 Event reminder SMS</li>
                                    <li class="list-group-item">📱 Bulk SMS campaigns</li>
                                    <li class="list-group-item">🚨 Urgent notifications</li>
                                    <li class="list-group-item">📊 SMS analytics & reporting</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="mt-4 text-center">
                            <a href="sms_integration.php" class="btn btn-primary me-2">
                                <i class="fas fa-cog me-2"></i>Configure SMS Settings
                            </a>
                            <a href="sms_campaigns.php" class="btn btn-success me-2">
                                <i class="fas fa-paper-plane me-2"></i>Create SMS Campaign
                            </a>
                            <a href="sms_analytics.php" class="btn btn-info">
                                <i class="fas fa-chart-bar me-2"></i>View SMS Analytics
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
