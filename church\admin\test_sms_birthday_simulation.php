<?php
/**
 * SMS Birthday Integration Simulation Test
 * 
 * This script simulates SMS sending for birthday reminders to test the integration
 * without actually sending SMS messages (useful when SMS providers aren't configured).
 */

require_once '../config.php';
require_once '../send_birthday_reminders.php';
require_once '../includes/sms_functions.php';

// Set headers for proper output
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SMS Birthday Simulation Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h2><i class="bi bi-play-circle"></i> SMS Birthday Simulation Test</h2>
                <p class="text-muted">Simulating SMS integration with birthday reminders (no actual SMS sent)</p>
                
                <?php
                if (isset($_POST['run_simulation'])) {
                    echo "<div class='card mb-4'>";
                    echo "<div class='card-header'><h5><i class='bi bi-gear'></i> Running SMS Birthday Simulation</h5></div>";
                    echo "<div class='card-body'>";
                    
                    try {
                        // Initialize birthday reminder system
                        $birthdayReminder = new BirthdayReminder($pdo);
                        
                        // Get today's birthday members
                        $birthdayMembers = $birthdayReminder->getUpcomingBirthdays(0);
                        
                        echo "<h6>Simulation Results:</h6>";
                        echo "<div class='alert alert-info'>";
                        echo "<strong>Found " . count($birthdayMembers) . " members with birthdays today</strong>";
                        echo "</div>";
                        
                        if (count($birthdayMembers) > 0) {
                            $simulatedSent = 0;
                            $simulatedFailed = 0;
                            
                            echo "<div class='table-responsive'>";
                            echo "<table class='table table-striped'>";
                            echo "<thead><tr><th>Member</th><th>Phone</th><th>SMS Status</th><th>Email Status</th><th>Action</th></tr></thead>";
                            echo "<tbody>";
                            
                            foreach ($birthdayMembers as $member) {
                                $smsStatus = "N/A";
                                $smsClass = "secondary";
                                $emailStatus = "Would Send";
                                $emailClass = "info";
                                
                                // Simulate SMS sending logic
                                if (!empty($member['phone_number'])) {
                                    if (isset($member['sms_notifications']) && $member['sms_notifications']) {
                                        // Simulate successful SMS
                                        $smsStatus = "Simulated Success";
                                        $smsClass = "success";
                                        $simulatedSent++;
                                        
                                        // Log simulated SMS to database
                                        try {
                                            $stmt = $pdo->prepare("INSERT INTO sms_logs (member_id, phone_number, message, sms_type, status, provider, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())");
                                            $message = "🎉 Happy Birthday " . $member['first_name'] . "! Wishing you a wonderful day filled with joy and blessings. - Freedom Assembly Church";
                                            $stmt->execute([
                                                $member['id'],
                                                $member['phone_number'],
                                                $message,
                                                'birthday',
                                                'sent',
                                                'simulation'
                                            ]);
                                        } catch (Exception $e) {
                                            // Ignore logging errors in simulation
                                        }
                                    } else {
                                        $smsStatus = "SMS Disabled";
                                        $smsClass = "warning";
                                    }
                                } else {
                                    $smsStatus = "No Phone";
                                    $smsClass = "secondary";
                                }
                                
                                echo "<tr>";
                                echo "<td>" . htmlspecialchars($member['full_name']) . "</td>";
                                echo "<td>" . htmlspecialchars($member['phone_number'] ?? 'N/A') . "</td>";
                                echo "<td><span class='badge bg-$smsClass'>$smsStatus</span></td>";
                                echo "<td><span class='badge bg-$emailClass'>$emailStatus</span></td>";
                                echo "<td><small>Birthday: " . date('M d', strtotime($member['birth_date'] ?? $member['date_of_birth'])) . "</small></td>";
                                echo "</tr>";
                            }
                            
                            echo "</tbody></table>";
                            echo "</div>";
                            
                            echo "<div class='row mt-3'>";
                            echo "<div class='col-md-6'>";
                            echo "<div class='card bg-success text-white'>";
                            echo "<div class='card-body text-center'>";
                            echo "<h4>$simulatedSent</h4>";
                            echo "<p>SMS Simulated Sent</p>";
                            echo "</div></div></div>";
                            
                            echo "<div class='col-md-6'>";
                            echo "<div class='card bg-primary text-white'>";
                            echo "<div class='card-body text-center'>";
                            echo "<h4>" . count($birthdayMembers) . "</h4>";
                            echo "<p>Emails Would Send</p>";
                            echo "</div></div></div>";
                            echo "</div>";
                            
                        } else {
                            echo "<div class='alert alert-warning'>";
                            echo "<i class='bi bi-info-circle'></i> No members have birthdays today to simulate.";
                            echo "</div>";
                        }
                        
                    } catch (Exception $e) {
                        echo "<div class='alert alert-danger'>";
                        echo "<h6><i class='bi bi-exclamation-triangle'></i> Simulation Error</h6>";
                        echo "<p>Error during simulation: " . htmlspecialchars($e->getMessage()) . "</p>";
                        echo "</div>";
                    }
                    
                    echo "</div></div>";
                    
                    // Show recent simulated SMS logs
                    echo "<div class='card mb-4'>";
                    echo "<div class='card-header'><h5><i class='bi bi-list'></i> Recent Simulated SMS Logs</h5></div>";
                    echo "<div class='card-body'>";
                    
                    try {
                        $stmt = $pdo->prepare("SELECT sl.*, m.full_name FROM sms_logs sl LEFT JOIN members m ON sl.member_id = m.id WHERE sl.provider = 'simulation' ORDER BY sl.created_at DESC LIMIT 10");
                        $stmt->execute();
                        $logs = $stmt->fetchAll();
                        
                        if (count($logs) > 0) {
                            echo "<div class='table-responsive'>";
                            echo "<table class='table table-sm'>";
                            echo "<thead><tr><th>Time</th><th>Member</th><th>Phone</th><th>Type</th><th>Status</th><th>Message Preview</th></tr></thead>";
                            echo "<tbody>";
                            
                            foreach ($logs as $log) {
                                $messagePreview = substr($log['message'], 0, 50) . (strlen($log['message']) > 50 ? '...' : '');
                                echo "<tr>";
                                echo "<td><small>" . date('M d, H:i', strtotime($log['created_at'])) . "</small></td>";
                                echo "<td>" . htmlspecialchars($log['full_name']) . "</td>";
                                echo "<td>" . htmlspecialchars($log['phone_number']) . "</td>";
                                echo "<td><span class='badge bg-info'>" . ucfirst($log['sms_type']) . "</span></td>";
                                echo "<td><span class='badge bg-success'>" . ucfirst($log['status']) . "</span></td>";
                                echo "<td><small>" . htmlspecialchars($messagePreview) . "</small></td>";
                                echo "</tr>";
                            }
                            
                            echo "</tbody></table>";
                            echo "</div>";
                        } else {
                            echo "<p class='text-muted'>No simulated SMS logs found.</p>";
                        }
                        
                    } catch (Exception $e) {
                        echo "<div class='alert alert-warning'>Error retrieving SMS logs: " . htmlspecialchars($e->getMessage()) . "</div>";
                    }
                    
                    echo "</div></div>";
                }
                ?>
                
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-play"></i> Run SMS Birthday Simulation</h5>
                    </div>
                    <div class="card-body">
                        <p>This simulation will:</p>
                        <ul>
                            <li>Find all members with birthdays today</li>
                            <li>Check their SMS notification preferences</li>
                            <li>Simulate sending birthday SMS messages</li>
                            <li>Log the simulated SMS activity</li>
                            <li>Show what would happen in a real birthday reminder run</li>
                        </ul>
                        
                        <form method="POST">
                            <button type="submit" name="run_simulation" class="btn btn-primary">
                                <i class="bi bi-play-circle"></i> Run Simulation
                            </button>
                        </form>
                    </div>
                </div>
                
                <div class="mt-4">
                    <a href="test_sms_birthday_integration.php" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> Back to Integration Test
                    </a>
                    <a href="sms_analytics.php" class="btn btn-info">
                        <i class="bi bi-graph-up"></i> View SMS Analytics
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
